#include <stdio.h>
#include <string.h>
#include "drv_htu21d.h"

#define TAG "htu21d"

#define HTU21D_ADDRESS (0x40 << 1)
#define REG_SOFTRESET 0xFE
#define REG_TEMP_MEASRE 0xF3
#define REG_HUMI_MEASURE 0xF5

static bool run_flag;

struct
{
    float temp;
    float humidity;
    float temp_offset;
    float humidity_offset;
} drv_htu21d_data;

static float _read_temp()
{
    esp_err_t err;
    uint8_t reg = REG_TEMP_MEASRE;
    uint8_t data[2];
    uint16_t temp_buff;
    float temp;

    err = i2c_bus_write_data(i2c_handler, HTU21D_ADDRESS, &reg, 1);

    vTaskDelay(pdMS_TO_TICKS(50));

    err = i2c_bus_read_bytes_directly(i2c_handler, HTU21D_ADDRESS, data, sizeof(data));

    if (err != ESP_OK)
        return drv_htu21d_data.temp;

    data[1] &= 0xFC;

    temp_buff = data[0] << 8 | data[1];
    temp = (float)(175.72f * temp_buff / 65535.0f - 46.85f);

    return temp;
}

static float _read_humidity()
{
    esp_err_t err;
    uint8_t reg = REG_HUMI_MEASURE;
    uint8_t data[2];
    uint16_t humi_buff;
    float humi;

    err = i2c_bus_write_data(i2c_handler, HTU21D_ADDRESS, &reg, 1);

    vTaskDelay(pdMS_TO_TICKS(50));

    err = i2c_bus_read_bytes_directly(i2c_handler, HTU21D_ADDRESS, data, sizeof(data));

    if (err != ESP_OK)
        return drv_htu21d_data.humidity;

    data[1] &= 0xFC;

    humi_buff = data[0] << 8 | data[1];
    humi = (float)(125.0f * humi_buff / 65535.0f - 6);

    if (humi > 100.0f)
        humi = 100.0f;

    return humi;
}

esp_err_t drv_htu21d_init()
{
    esp_err_t err = ESP_OK;
    run_flag = false;

    if (i2c_handler == NULL)
        return err;

    err = i2c_bus_probe_addr(i2c_handler, HTU21D_ADDRESS);
    if (err == ESP_OK)
    {
        ESP_LOGI(TAG, "detected htu21d device");

        uint8_t reg_reset = REG_SOFTRESET;
        i2c_bus_write_data(i2c_handler, HTU21D_ADDRESS, &reg_reset, 1);
        vTaskDelay(pdMS_TO_TICKS(15));
    }
    else
    {
        ESP_LOGI(TAG, "htu21d device has not detected");
    }

    drv_htu21d_data.temp = _read_temp();
    drv_htu21d_data.humidity = _read_humidity();

    return err;
}

void drv_htu21d_deinit()
{
    run_flag = false;
    drv_htu21d_data.temp = 999;
    drv_htu21d_data.humidity = 999;
}

void drv_htu21d_task(void *pvParameters)
{
    periph_service_handle_t serv_handle = (periph_service_handle_t)pvParameters;
    int delay = periph_service_get_data(serv_handle);

    if (i2c_handler == NULL)
        return;

    run_flag = true;
    while (run_flag)
    {
        if (!run_flag)
            break;

        drv_htu21d_data.temp = _read_temp();
        drv_htu21d_data.humidity = _read_humidity();

        // ESP_LOGI(TAG, "htu21d task %f,%f %d", drv_htu21d_data.temp, drv_htu21d_data.humidity, delay);

        vTaskDelay(pdMS_TO_TICKS(delay));
    }

    ESP_LOGI(TAG, "htu21d service destroyed");
    vTaskDelete(NULL);
}

float drv_htu21d_temp(void)
{
    drv_htu21d_data.temp += drv_htu21d_data.temp_offset;
    return drv_htu21d_data.temp;
}

float drv_htu21d_humidity(void)
{
    drv_htu21d_data.humidity += drv_htu21d_data.humidity_offset;
    return drv_htu21d_data.humidity;
}

bool drv_htu21d_detect(void)
{
    esp_err_t err;

    err = i2c_bus_probe_addr(i2c_handler, HTU21D_ADDRESS);

    if (err == ESP_OK)
        return true;

    return false;
}

uint8_t drv_htu21d_get_addr(void)
{
    return (HTU21D_ADDRESS >> 1);
}

void drv_htu21d_set_offset(float temp, float humi)
{
    drv_htu21d_data.temp_offset = temp;
    drv_htu21d_data.humidity_offset = humi;
}
#include <stdio.h>
#include "drv_key.h"
#include "drv_pca9557.h"
#include "board_pins_user.h"
#include "driver/gpio.h"
#include "esp_log.h"

#define TAG "drv_key"

void drv_key_init()
{
#if (DEF_KEY_A_GPIO >= 0)
    gpio_config_t keya_gpio_config = {
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = 1ULL << DEF_KEY_A_GPIO,
        .pull_up_en = GPIO_PULLUP_ENABLE};
    ESP_ERROR_CHECK(gpio_config(&keya_gpio_config));
#endif

#if (DEF_KEY_B_GPIO >= 0)
    gpio_config_t keyb_gpio_config = {
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = 1ULL << DEF_KEY_B_GPIO,
        .pull_up_en = GPIO_PULLUP_ENABLE};
    ESP_ERROR_CHECK(gpio_config(&keyb_gpio_config));
#endif

#if (DEF_KEY_C_GPIO >= 0)
    gpio_config_t keyc_gpio_config = {
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = 1ULL << DEF_KEY_C_GPIO,
        .pull_up_en = GPIO_PULLUP_ENABLE};
    ESP_ERROR_CHECK(gpio_config(&keyc_gpio_config));
#endif

    //KEY CHAT
    drv_pca9557_pinmode(EXTEND_PIN_2_CHAT_LISTEN, EXTEND_INPUT);
}

int drv_key_read()
{
    int key_code = DRV_KEY_NONE;

    if (gpio_get_level(DEF_KEY_A_GPIO) == 0)
        key_code = DRV_KEY_PREV;
    else if (gpio_get_level(DEF_KEY_B_GPIO) == 0)
        key_code = DRV_KEY_ENTER;
    else if (gpio_get_level(DEF_KEY_C_GPIO) == 0)
        key_code = DRV_KEY_NEXT;
    else if(drv_pca9557_read(EXTEND_PIN_2_CHAT_LISTEN) == 0)
        key_code = DRV_KEY_CHAT;

    return key_code;
}

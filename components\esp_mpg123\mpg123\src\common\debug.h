/*
	Some macros for printing debugging/warning/error messages with location info.

	public domain (or LGPL / GPL, if you like that more;-)
	generated by debugdef.pl, what was
	trivially written by <PERSON> <<EMAIL>>
	
	You decide to define DEBUG or not and write debug("foo") or
	mdebug("foo: %s", message) and variants with more arguments and this
	is converted to an elaborate call to fprintf(stderr, ...) that prints your
	message with a line end attached and with information on where the call
	occured.
*/

#include "config.h"

#ifdef ME
#define DBGPRFX ME": "
#else
#define DBGPRFX ""
#endif

#define noop ((void)0)

#ifdef XDEBUG
#define mxdebug(s, ...) mdebug(s, __VA_ARGS__)
#define xdebug(s) debug(s)
#else
#define mxdebug(s, ...) noop
#define xdebug(s) noop
#endif

// Used for debugging and warning messages.
#include <stdio.h>
#define debug_print(t, s, ...) fprintf(stderr, DBGPRFX "[" __FILE__ ":%s():%i] " t ": " s "\n", __func__, __LINE__, __VA_ARGS__)

#ifdef DEBUG
#define mdebug(s, ...) debug_print("debug", s, __VA_ARGS__)
#else
#define mdebug(s, ...) noop
#endif
#define debug(s) mdebug("%s", s)

// Print warning message.
#ifndef NO_WARNING
#define mwarning(s, ...) debug_print("warning", s, __VA_ARGS__)
#else
#define mwarning(s, ...) 
#endif
#define warning(s) mwarning("%s", s)

// Print error message.
#ifndef NO_ERRORMSG
#define merror(s, ...) debug_print("error", s, __VA_ARGS__)
#else
#define merror(s, ...) noop
#endif
#define error(s) merror("%s", s)

// Print error message and return given value.
#ifndef NO_ERETURN
#define mereturn(rv, s, ...) do{ debug_print("ereturn", s, __VA_ARGS__); return rv; } while (0)
#else
#define mereturn(rv, s, ...) return rv
#endif
#define ereturn(rv, s) mereturn(rv, "%s", s)

// For old code that had to hardcode the argument number.

#define debug1(s, a) mdebug(s,a)
#define debug2(s, a, b) mdebug(s, a, b)
#define debug3(s, a, b, c) mdebug(s, a, b, c)
#define debug4(s, a, b, c, d) mdebug(s, a, b, c, d)
#define debug5(s, a, b, c, d, e) mdebug(s, a, b, c, d, e)
#define debug6(s, a, b, c, d, e, f) mdebug(s, a, b, c, d, e, f)
#define debug7(s, a, b, c, d, e, f, g) mdebug(s, a, b, c, d, e, f, g)
#define debug8(s, a, b, c, d, e, f, g, h) mdebug(s, a, b, c, d, e, f, g, h)
#define debug9(s, a, b, c, d, e, f, g, h, i) mdebug(s, a, b, c, d, e, f, g, h, i)
#define debug10(s, a, b, c, d, e, f, g, h, i, j) mdebug(s, a, b, c, d, e, f, g, h, i, j)
#define debug11(s, a, b, c, d, e, f, g, h, i, j, k) mdebug(s, a, b, c, d, e, f, g, h, i, j, k)
#define debug12(s, a, b, c, d, e, f, g, h, i, j, k, l) mdebug(s, a, b, c, d, e, f, g, h, i, j, k, l)
#define debug13(s, a, b, c, d, e, f, g, h, i, j, k, l, m) mdebug(s, a, b, c, d, e, f, g, h, i, j, k, l, m)
#define debug14(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n) mdebug(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n)
#define debug15(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o) mdebug(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)

#define warning1(s, a) mwarning(s, a)
#define warning2(s, a, b) mwarning(s, a, b)
#define warning3(s, a, b, c) mwarning(s, a, b, c)
#define warning4(s, a, b, c, d) mwarning(s, a, b, c, d)
#define warning5(s, a, b, c, d, e) mwarning(s, a, b, c, d, e)
#define warning6(s, a, b, c, d, e, f) mwarning(s, a, b, c, d, e, f)
#define warning7(s, a, b, c, d, e, f, g) mwarning(s, a, b, c, d, e, f, g)
#define warning8(s, a, b, c, d, e, f, g, h) mwarning(s, a, b, c, d, e, f, g, h)
#define warning9(s, a, b, c, d, e, f, g, h, i) mwarning(s, a, b, c, d, e, f, g, h, i)
#define warning10(s, a, b, c, d, e, f, g, h, i, j) mwarning(s, a, b, c, d, e, f, g, h, i, j)
#define warning11(s, a, b, c, d, e, f, g, h, i, j, k) mwarning(s, a, b, c, d, e, f, g, h, i, j, k)
#define warning12(s, a, b, c, d, e, f, g, h, i, j, k, l) mwarning(s, a, b, c, d, e, f, g, h, i, j, k, l)
#define warning13(s, a, b, c, d, e, f, g, h, i, j, k, l, m) mwarning(s, a, b, c, d, e, f, g, h, i, j, k, l, m)
#define warning14(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n) mwarning(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n)
#define warning15(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o) mwarning(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)

#define error1(s, a) merror(s, a)
#define error2(s, a, b) merror(s, a, b)
#define error3(s, a, b, c) merror(s, a, b, c)
#define error4(s, a, b, c, d) merror(s, a, b, c, d)
#define error5(s, a, b, c, d, e) merror(s, a, b, c, d, e)
#define error6(s, a, b, c, d, e, f) merror(s, a, b, c, d, e, f)
#define error7(s, a, b, c, d, e, f, g) merror(s, a, b, c, d, e, f, g)
#define error8(s, a, b, c, d, e, f, g, h) merror(s, a, b, c, d, e, f, g, h)
#define error9(s, a, b, c, d, e, f, g, h, i) merror(s, a, b, c, d, e, f, g, h, i)
#define error10(s, a, b, c, d, e, f, g, h, i, j) merror(s, a, b, c, d, e, f, g, h, i, j)
#define error11(s, a, b, c, d, e, f, g, h, i, j, k) merror(s, a, b, c, d, e, f, g, h, i, j, k)
#define error12(s, a, b, c, d, e, f, g, h, i, j, k, l) merror(s, a, b, c, d, e, f, g, h, i, j, k, l)
#define error13(s, a, b, c, d, e, f, g, h, i, j, k, l, m) merror(s, a, b, c, d, e, f, g, h, i, j, k, l, m)
#define error14(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n) merror(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n)
#define error15(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o) merror(s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)

#define ereturn1(rv, s, a) mereturn(rv, s, a)
#define ereturn2(rv, s, a, b) mereturn(rv, s, a, b)
#define ereturn3(rv, s, a, b, c) mereturn(rv, s, a, b, c)
#define ereturn4(rv, s, a, b, c, d) mereturn(rv, s, a, b, c, d)
#define ereturn5(rv, s, a, b, c, d, e) mereturn(rv, s, a, b, c, d, e)
#define ereturn6(rv, s, a, b, c, d, e, f) mereturn(rv, s, a, b, c, d, e, f)
#define ereturn7(rv, s, a, b, c, d, e, f, g) mereturn(rv, s, a, b, c, d, e, f, g)
#define ereturn8(rv, s, a, b, c, d, e, f, g, h) mereturn(rv, s, a, b, c, d, e, f, g, h)
#define ereturn9(rv, s, a, b, c, d, e, f, g, h, i) mereturn(rv, s, a, b, c, d, e, f, g, h, i)
#define ereturn10(rv, s, a, b, c, d, e, f, g, h, i, j) mereturn(rv, s, a, b, c, d, e, f, g, h, i, j)
#define ereturn11(rv, s, a, b, c, d, e, f, g, h, i, j, k) mereturn(rv, s, a, b, c, d, e, f, g, h, i, j, k)
#define ereturn12(rv, s, a, b, c, d, e, f, g, h, i, j, k, l) mereturn(rv, s, a, b, c, d, e, f, g, h, i, j, k, l)
#define ereturn13(rv, s, a, b, c, d, e, f, g, h, i, j, k, l, m) mereturn(rv, s, a, b, c, d, e, f, g, h, i, j, k, l, m)
#define ereturn14(rv, s, a, b, c, d, e, f, g, h, i, j, k, l, m, n) mereturn(rv, s, a, b, c, d, e, f, g, h, i, j, k, l, m, n)
#define ereturn15(rv, s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o) mereturn(rv, s, a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)

{"COMPONENT_KCONFIGS": "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_common/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/freertos/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/hal/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/log/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/newlib/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/soc/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/esp_app_format/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/esptool_py/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader/sdkconfig.rename;E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sdkconfig.rename;E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sdkconfig.rename.esp32s3;E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/sdkconfig.rename;E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/sdkconfig.rename.esp32s3;E:/Espressif/533/v5.3.3/esp-idf/components/esptool_py/sdkconfig.rename;E:/Espressif/533/v5.3.3/esp-idf/components/freertos/sdkconfig.rename;E:/Espressif/533/v5.3.3/esp-idf/components/hal/sdkconfig.rename;E:/Espressif/533/v5.3.3/esp-idf/components/newlib/sdkconfig.rename.esp32s3;E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32s3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.3.3", "IDF_ENV_FPGA": "", "IDF_PATH": "E:/Espressif/533/v5.3.3/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/bootloader/kconfigs_projbuild.in"}
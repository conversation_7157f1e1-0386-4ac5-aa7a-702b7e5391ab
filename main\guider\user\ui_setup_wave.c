#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

static const char *TAG = "wave_setup";

// 外部函数声明
extern void wave_set_display_type(wave_data_type_t type);

/**
 * @brief 下拉框选择事件处理
 */
static void wave_dropdown_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);

    if (code == LV_EVENT_VALUE_CHANGED) {
        uint16_t selected = lv_dropdown_get_selected(obj);
        wave_data_type_t wave_type;

        // 根据下拉框选择映射到波形数据类型
        switch (selected) {
            case 0:
                wave_type = WAVE_DATA_CURRENT;
                break;
            case 1:
                wave_type = WAVE_DATA_SPEED;
                break;
            case 2:
                wave_type = WAVE_DATA_POSITION;
                break;
            case 3:
                wave_type = WAVE_DATA_VOLTAGE;
                break;
            case 4:
                wave_type = WAVE_DATA_TEMPERATURE;
                break;
            default:
                wave_type = WAVE_DATA_CURRENT;
                break;
        }

        // 切换波形显示类型
        wave_set_display_type(wave_type);

        ESP_LOGI(TAG, "Wave type changed to: %d, display refreshed", wave_type);
    }
}

/**
 * @brief 自动缩放按键事件处理 - 修改为开关模式
 */
static void Wave_Page_btn_auto_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "Auto scale toggle button clicked");

        // 切换自动缩放开关状态
        wave_toggle_auto_scale();

        ESP_LOGI(TAG, "Auto scale toggle completed");
        break;
    }
    default:
        break;
    }
}

/**
 * @brief 分辨率增加按键事件处理（减少点数，提高分辨率）
 */
static void Wave_Page_btn_biggeer_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "Resolution increase button clicked");

        // 增加分辨率（减少点数）
        wave_increase_resolution();

        ESP_LOGI(TAG, "Resolution increase completed");
        break;
    }
    default:
        break;
    }
}

/**
 * @brief 分辨率减少按键事件处理（增加点数，降低分辨率）
 */
static void Wave_Page_btn_smaller_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "Resolution decrease button clicked");

        // 减少分辨率（增加点数）
        wave_decrease_resolution();

        ESP_LOGI(TAG, "Resolution decrease completed");
        break;
    }
    default:
        break;
    }
}

/**
 * @brief 初始化波形页面
 */
static void wave_page_init(lv_ui *ui)
{
    // 更新下拉框选项，添加更多数据类型
    lv_dropdown_set_options(ui->Wave_Page_ddlist_1,
        "力矩环\n速度环\n位置环\n速度轨迹环\n位置轨迹环");

    // 设置默认选择
    lv_dropdown_set_selected(ui->Wave_Page_ddlist_1, 0);

    // 初始化图表设置
    if (ui->Wave_Page_chart_1) {
        // 设置图表为实时更新模式
        lv_chart_set_update_mode(ui->Wave_Page_chart_1, LV_CHART_UPDATE_MODE_SHIFT);

        // 设置点数
        lv_chart_set_point_count(ui->Wave_Page_chart_1, 50);

        // 创建数据系列 - 这是关键的修复
        ui->Wave_Page_chart_1_series = lv_chart_add_series(ui->Wave_Page_chart_1,
                                                           lv_color_hex(0xFF0034),
                                                           LV_CHART_AXIS_PRIMARY_Y);

        if (!ui->Wave_Page_chart_1_series) {
            ESP_LOGE(TAG, "Failed to create chart series!");
            return;
        }
    }

    // 初始化按键颜色状态
    if (ui->Wave_Page_btn_auto) {
        // 自动缩放按键初始为蓝色（关闭状态）
        lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x0000FF), LV_STATE_DEFAULT);
    }

    // 初始化显示电流波形（与下拉框默认选择保持一致）
    wave_set_display_type(WAVE_DATA_CURRENT);

    ESP_LOGI(TAG, "Wave page initialized with current waveform");
}

void events_init_Wave_Page (lv_ui *ui)
{
    // 返回按钮事件
    lv_obj_add_event_cb(ui->Wave_Page_imgbtn_1, back_to_menu_event_handler, LV_EVENT_ALL, ui);

    // 下拉框选择事件
    lv_obj_add_event_cb(ui->Wave_Page_ddlist_1, wave_dropdown_event_handler, LV_EVENT_ALL, ui);

    // 自动缩放开关按键事件
    lv_obj_add_event_cb(ui->Wave_Page_btn_auto, Wave_Page_btn_auto_event_handler, LV_EVENT_ALL, ui);

    // 分辨率增加按键事件（+按键）
    lv_obj_add_event_cb(ui->Wave_Page_btn_biggeer, Wave_Page_btn_biggeer_event_handler, LV_EVENT_ALL, ui);

    // 分辨率减少按键事件（-按键）
    lv_obj_add_event_cb(ui->Wave_Page_btn_smaller, Wave_Page_btn_smaller_event_handler, LV_EVENT_ALL, ui);

    // 初始化波形页面
    wave_page_init(ui);
}
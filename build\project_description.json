{"version": "1.2", "project_name": "DDClock", "project_version": "1.0.0.2", "project_path": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock", "idf_path": "E:/Espressif/533/v5.3.3/esp-idf", "build_dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build", "config_file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/sdkconfig", "config_defaults": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/sdkconfig.defaults", "bootloader_elf": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/bootloader/bootloader.elf", "app_elf": "DDClock.elf", "app_bin": "DDClock.bin", "build_type": "flash_app", "git_revision": "v5.3.3", "target": "esp32s3", "rev": "", "min_rev": "0", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "c_compiler": "E:/Espressif/533/tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/bt/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/console/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/driver/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/efuse/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_common/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ana_cmpr/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_cam/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_dac/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gptimer/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_isp/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_jpeg/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ledc/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_parlio/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_pcnt/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdm/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_touch_sens/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_tsens/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_uart/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_event/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_ota/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_server/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_partition/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_ringbuf/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/freertos/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/hal/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/heap/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/ieee802154/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/log/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/lwip/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/mbedtls/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/newlib/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/nvs_sec_provider/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/openthread/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/pthread/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/soc/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/ulp/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/unity/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/usb/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/vfs/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/Kconfig;E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/Kconfig;E:/Espressif/esp-adf/components/dueros_service/Kconfig;E:/Espressif/esp-adf/components/esp_codec_dev/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_battery/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_encoder/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_lcd_i80/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_wifi/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__cmake_utilities/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_websocket_client/Kconfig;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__jsmn/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/esp_app_format/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/esptool_py/Kconfig.projbuild;E:/Espressif/533/v5.3.3/esp-idf/components/partition_table/Kconfig.projbuild;E:/Espressif/esp-adf/components/audio_board/Kconfig.projbuild;E:/Espressif/esp-adf/components/esp-adf-libs/Kconfig.projbuild;E:/Espressif/esp-adf/components/esp-sr/Kconfig.projbuild;E:/Espressif/esp-adf/components/esp_dispatcher/Kconfig.projbuild;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board/Kconfig.projbuild;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["78__esp-opus", "adf_utils", "app_trace", "app_update", "audio_board", "audio_hal", "audio_mixer", "audio_pipeline", "audio_recorder", "audio_sal", "audio_stream", "battery_service", "bluetooth_service", "board", "bootloader", "bootloader_support", "bt", "clouds", "cmock", "console", "coredump_upload_service", "cxx", "display_service", "driver", "drv_adc", "drv_battery", "drv_beep", "drv_bt", "drv_encoder", "drv_htu21d", "drv_key", "drv_lcd_i80", "drv_pca9557", "drv_rtc", "dueros_service", "efuse", "esp-adf-libs", "esp-ml307", "esp-sr", "esp-tls", "esp_actions", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_codec_dev", "esp_coex", "esp_common", "esp_coze", "esp_dispatcher", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_event_cast", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_mpg123", "esp_netif", "esp_netif_stack", "esp_ogg", "esp_partition", "esp_peripherals", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rlottie", "esp_rom", "esp_simple_dsp", "esp_system", "esp_timer", "esp_vfs_console", "esp_vorbis", "esp_wifi", "espcoredump", "espressif__cmake_utilities", "espressif__dl_fft", "espressif__esp-dsp", "espressif__esp_lcd_ili9341", "espressif__esp_websocket_client", "espressif__jsmn", "espressif__nghttp", "espressif__zlib", "esptool_py", "fatfs", "freertos", "gzip_miniz", "hal", "hal_i2c", "hal_service", "hal_wifi", "heap", "http_parser", "idf_http_rest_client", "idf_test", "ieee802154", "input_key_service", "json", "list", "log", "lvgl", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "ota_service", "partition_table", "perfmon", "player", "playlist", "protobuf-c", "protocomm", "pthread", "qweather", "recorder", "res_binary", "sdmmc", "sntp_service", "soc", "spi_flash", "spiffs", "tcp_transport", "tone_partition", "touch_element", "ulp", "unity", "usb", "utils", "vfs", "wear_levelling", "wifi_provisioning", "wifi_service", "wpa_supplicant", "xtensa", "xzai", ""], "build_component_paths": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus", "E:/Espressif/esp-adf/components/adf_utils", "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace", "E:/Espressif/533/v5.3.3/esp-idf/components/app_update", "E:/Espressif/esp-adf/components/audio_board", "E:/Espressif/esp-adf/components/audio_hal", "E:/Espressif/esp-adf/components/audio_mixer", "E:/Espressif/esp-adf/components/audio_pipeline", "E:/Espressif/esp-adf/components/audio_recorder", "E:/Espressif/esp-adf/components/audio_sal", "E:/Espressif/esp-adf/components/audio_stream", "E:/Espressif/esp-adf/components/battery_service", "E:/Espressif/esp-adf/components/bluetooth_service", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support", "E:/Espressif/533/v5.3.3/esp-idf/components/bt", "E:/Espressif/esp-adf/components/clouds", "E:/Espressif/533/v5.3.3/esp-idf/components/cmock", "E:/Espressif/533/v5.3.3/esp-idf/components/console", "E:/Espressif/esp-adf/components/coredump_upload_service", "E:/Espressif/533/v5.3.3/esp-idf/components/cxx", "E:/Espressif/esp-adf/components/display_service", "E:/Espressif/533/v5.3.3/esp-idf/components/driver", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_adc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_battery", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_beep", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_bt", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_encoder", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_htu21d", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_key", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_lcd_i80", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_pca9557", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_rtc", "E:/Espressif/esp-adf/components/dueros_service", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse", "E:/Espressif/esp-adf/components/esp-adf-libs", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307", "E:/Espressif/esp-adf/components/esp-sr", "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls", "E:/Espressif/esp-adf/components/esp_actions", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_app_format", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_bootloader_format", "E:/Espressif/esp-adf/components/esp_codec_dev", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_common", "E:/Espressif/esp-adf/components/esp_coze", "E:/Espressif/esp-adf/components/esp_dispatcher", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ana_cmpr", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_cam", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_dac", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gptimer", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_isp", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_jpeg", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ledc", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_parlio", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_pcnt", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ppa", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdio", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdm", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdmmc", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdspi", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_touch_sens", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_tsens", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_uart", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_event", "E:/Espressif/esp-adf/components/esp_event_cast", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_ota", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_server", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_mpg123", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif_stack", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_ogg", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_partition", "E:/Espressif/esp-adf/components/esp_peripherals", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_ringbuf", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_rlottie", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_simple_dsp", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_vfs_console", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__cmake_utilities", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_lcd_ili9341", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_websocket_client", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__jsmn", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib", "E:/Espressif/533/v5.3.3/esp-idf/components/esptool_py", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/gzip_miniz", "E:/Espressif/533/v5.3.3/esp-idf/components/hal", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_i2c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_service", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_wifi", "E:/Espressif/533/v5.3.3/esp-idf/components/heap", "E:/Espressif/533/v5.3.3/esp-idf/components/http_parser", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client", "E:/Espressif/533/v5.3.3/esp-idf/components/idf_test", "E:/Espressif/533/v5.3.3/esp-idf/components/ieee802154", "E:/Espressif/esp-adf/components/input_key_service", "E:/Espressif/533/v5.3.3/esp-idf/components/json", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/list", "E:/Espressif/533/v5.3.3/esp-idf/components/log", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main", "E:/Espressif/533/v5.3.3/esp-idf/components/mbedtls", "E:/Espressif/533/v5.3.3/esp-idf/components/mqtt", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_sec_provider", "E:/Espressif/533/v5.3.3/esp-idf/components/openthread", "E:/Espressif/esp-adf/components/ota_service", "E:/Espressif/533/v5.3.3/esp-idf/components/partition_table", "E:/Espressif/533/v5.3.3/esp-idf/components/perfmon", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/player", "E:/Espressif/esp-adf/components/playlist", "E:/Espressif/533/v5.3.3/esp-idf/components/protobuf-c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm", "E:/Espressif/533/v5.3.3/esp-idf/components/pthread", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/qweather", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/recorder", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/res_binary", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/sntp_service", "E:/Espressif/533/v5.3.3/esp-idf/components/soc", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs", "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport", "E:/Espressif/esp-adf/components/tone_partition", "E:/Espressif/533/v5.3.3/esp-idf/components/touch_element", "E:/Espressif/533/v5.3.3/esp-idf/components/ulp", "E:/Espressif/533/v5.3.3/esp-idf/components/unity", "E:/Espressif/533/v5.3.3/esp-idf/components/usb", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils", "E:/Espressif/533/v5.3.3/esp-idf/components/vfs", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning", "E:/Espressif/esp-adf/components/wifi_service", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant", "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai", ""], "build_component_info": {"78__esp-opus": {"alias": "idf::78__esp-opus", "target": "___idf_78__esp-opus", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus", "type": "LIBRARY", "lib": "__idf_78__esp-opus", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/78__esp-opus/lib78__esp-opus.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/bands.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/celt.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/celt_encoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/celt_decoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/cwrs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/entcode.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/entdec.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/entenc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/kiss_fft.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/laplace.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/mathops.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/mdct.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/modes.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/pitch.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/celt_lpc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/quant_bands.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/rate.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/celt/vq.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_decoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_encoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/extensions.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_multistream.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_multistream_encoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_multistream_decoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/repacketizer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_projection_encoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/opus_projection_decoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/src/mapping_matrix.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/CNG.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/code_signs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/init_decoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decode_core.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decode_frame.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decode_parameters.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decode_indices.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decode_pulses.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decoder_set_fs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/dec_API.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/enc_API.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/encode_indices.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/encode_pulses.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/gain_quant.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/interpolate.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/LP_variable_cutoff.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_decode.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NSQ.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NSQ_del_dec.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/PLC.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/shell_coder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_gain.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_LTP.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_NLSF_CB_NB_MB.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_NLSF_CB_WB.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_other.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_pitch_lag.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/tables_pulses_per_block.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/VAD.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/control_audio_bandwidth.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/quant_LTP_gains.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/VQ_WMat_EC.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/HP_variable_cutoff.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_encode.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_VQ.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_unpack.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_del_dec_quant.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/process_NLSFs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/stereo_LR_to_MS.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/stereo_MS_to_LR.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/check_control_input.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/control_SNR.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/init_encoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/control_codec.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/A2NLSF.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/ana_filt_bank_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/biquad_alt.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/bwexpander_32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/bwexpander.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/debug.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/decode_pitch.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/inner_prod_aligned.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/lin2log.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/log2lin.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/LPC_analysis_filter.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/LPC_inv_pred_gain.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/table_LSF_cos.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF2A.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_stabilize.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/NLSF_VQ_weights_laroia.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/pitch_est_tables.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_down2_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_down2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_private_AR2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_private_down_FIR.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_private_IIR_FIR.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_private_up2_HQ.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/resampler_rom.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/sigm_Q15.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/sort.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/sum_sqr_shift.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/stereo_decode_pred.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/stereo_encode_pred.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/stereo_find_predictor.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/stereo_quant_pred.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/LPC_fit.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/LTP_analysis_filter_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/LTP_scale_ctrl_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/corrMatrix_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/encode_frame_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/find_LPC_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/find_LTP_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/find_pitch_lags_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/find_pred_coefs_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/noise_shape_analysis_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/process_gains_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/regularize_correlations_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/residual_energy16_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/residual_energy_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/warped_autocorrelation_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/apply_sine_window_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/autocorr_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/burg_modified_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/k2a_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/k2a_Q16_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/pitch_analysis_core_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/vector_ops_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/schur64_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/fixed/schur_FIX.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus/silk/arm/arm_silk_map.c"], "include_dirs": ["include"]}, "adf_utils": {"alias": "idf::adf_utils", "target": "___idf_adf_utils", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/adf_utils", "type": "LIBRARY", "lib": "__idf_adf_utils", "reqs": [], "priv_reqs": ["esp_http_client", "espressif__jsmn", "mbedtls", "audio_sal"], "managed_reqs": [], "managed_priv_reqs": ["espressif__jsmn"], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/adf_utils/libadf_utils.a", "sources": ["E:/Espressif/esp-adf/components/adf_utils/json_utils.c", "E:/Espressif/esp-adf/components/adf_utils/cloud_services/aws_sig_v4_signing.c", "E:/Espressif/esp-adf/components/adf_utils/cloud_services/baidu_access_token.c"], "include_dirs": ["cloud_services/include", "include"]}, "app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/app_trace/libapp_trace.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/app_trace/app_trace.c", "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace/app_trace_util.c", "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace/host_file_io.c", "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/app_update/libapp_update.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/app_update/esp_ota_ops.c", "E:/Espressif/533/v5.3.3/esp-idf/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "audio_board": {"alias": "idf::audio_board", "target": "___idf_audio_board", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_board", "type": "CONFIG_ONLY", "lib": "__idf_audio_board", "reqs": [], "priv_reqs": ["esp_peripherals", "audio_sal", "audio_hal", "esp_dispatcher", "display_service", "espressif__esp_lcd_ili9341"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_lcd_ili9341"], "file": "", "sources": [], "include_dirs": ["./include"]}, "audio_hal": {"alias": "idf::audio_hal", "target": "___idf_audio_hal", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_hal", "type": "LIBRARY", "lib": "__idf_audio_hal", "reqs": [], "priv_reqs": ["audio_sal", "audio_board", "mbedtls", "esp_peripherals", "display_service", "esp_dispatcher"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/audio_hal/libaudio_hal.a", "sources": ["E:/Espressif/esp-adf/components/audio_hal/audio_hal.c", "E:/Espressif/esp-adf/components/audio_hal/audio_volume.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es8388/es8388.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es8388/headphone_detect.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es8374/es8374.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es8311/es8311.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es8156/es8156.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es7243/es7243.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es7148/es7148.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es7210/es7210.c", "E:/Espressif/esp-adf/components/audio_hal/driver/es7243e/es7243e.c", "E:/Espressif/esp-adf/components/audio_hal/driver/tas5805m/tas5805m.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/zl38063.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/api_lib/vprocTwolf_access.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/api_lib/vproc_common.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/example_apps/tw_hal_verify.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/example_apps/tw_ldcfg.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/example_apps/tw_ldfw.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/example_apps/tw_ldfwcfg.c", "E:/Espressif/esp-adf/components/audio_hal/driver/zl38063/example_apps/tw_spi_access.c"], "include_dirs": ["./include", "./driver/es8388", "./driver/es8374", "./driver/es8311", "./driver/es8156", "./driver/es7243", "./driver/es7148", "./driver/es7210", "./driver/es7243e", "./driver/tas5805m", "./driver/include", "./driver/zl38063", "./driver/zl38063/api_lib", "./driver/zl38063/example_apps", "./driver/zl38063/firmware"]}, "audio_mixer": {"alias": "idf::audio_mixer", "target": "___idf_audio_mixer", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_mixer", "type": "LIBRARY", "lib": "__idf_audio_mixer", "reqs": ["audio_sal", "esp-adf-libs"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/audio_mixer/libaudio_mixer.a", "sources": ["E:/Espressif/esp-adf/components/audio_mixer/audio_mixer.c"], "include_dirs": ["include"]}, "audio_pipeline": {"alias": "idf::audio_pipeline", "target": "___idf_audio_pipeline", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_pipeline", "type": "LIBRARY", "lib": "__idf_audio_pipeline", "reqs": ["audio_sal", "esp-adf-libs"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/audio_pipeline/libaudio_pipeline.a", "sources": ["E:/Espressif/esp-adf/components/audio_pipeline/audio_element.c", "E:/Espressif/esp-adf/components/audio_pipeline/audio_event_iface.c", "E:/Espressif/esp-adf/components/audio_pipeline/audio_pipeline.c", "E:/Espressif/esp-adf/components/audio_pipeline/ringbuf.c"], "include_dirs": ["include"]}, "audio_recorder": {"alias": "idf::audio_recorder", "target": "___idf_audio_recorder", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_recorder", "type": "LIBRARY", "lib": "__idf_audio_recorder", "reqs": ["audio_sal", "audio_pipeline", "esp-sr", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/audio_recorder/libaudio_recorder.a", "sources": ["E:/Espressif/esp-adf/components/audio_recorder/recorder_encoder.c", "E:/Espressif/esp-adf/components/audio_recorder/audio_recorder.c", "E:/Espressif/esp-adf/components/audio_recorder/recorder_sr.c"], "include_dirs": ["include"]}, "audio_sal": {"alias": "idf::audio_sal", "target": "___idf_audio_sal", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_sal", "type": "LIBRARY", "lib": "__idf_audio_sal", "reqs": ["efuse"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/audio_sal/libaudio_sal.a", "sources": ["E:/Espressif/esp-adf/components/audio_sal/audio_mem.c", "E:/Espressif/esp-adf/components/audio_sal/audio_sys.c", "E:/Espressif/esp-adf/components/audio_sal/audio_thread.c", "E:/Espressif/esp-adf/components/audio_sal/audio_url.c", "E:/Espressif/esp-adf/components/audio_sal/audio_mutex.c", "E:/Espressif/esp-adf/components/audio_sal/audio_queue.c", "E:/Espressif/esp-adf/components/audio_sal/media_os_ctype.c"], "include_dirs": ["include"]}, "audio_stream": {"alias": "idf::audio_stream", "target": "___idf_audio_stream", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_stream", "type": "LIBRARY", "lib": "__idf_audio_stream", "reqs": ["audio_pipeline", "driver", "audio_sal", "esp_http_client", "tcp_transport", "spiffs", "audio_board", "esp-adf-libs", "bootloader_support", "esp_dispatcher", "esp_actions", "tone_partition", "mbedtls", "esp-sr"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/audio_stream/libaudio_stream.a", "sources": ["E:/Espressif/esp-adf/components/audio_stream/fatfs_stream.c", "E:/Espressif/esp-adf/components/audio_stream/http_stream.c", "E:/Espressif/esp-adf/components/audio_stream/http_playlist.c", "E:/Espressif/esp-adf/components/audio_stream/raw_stream.c", "E:/Espressif/esp-adf/components/audio_stream/spiffs_stream.c", "E:/Espressif/esp-adf/components/audio_stream/tone_stream.c", "E:/Espressif/esp-adf/components/audio_stream/tcp_client_stream.c", "E:/Espressif/esp-adf/components/audio_stream/embed_flash_stream.c", "E:/Espressif/esp-adf/components/audio_stream/pwm_stream.c", "E:/Espressif/esp-adf/components/audio_stream/lib/hls/hls_parse.c", "E:/Espressif/esp-adf/components/audio_stream/lib/hls/hls_playlist.c", "E:/Espressif/esp-adf/components/audio_stream/lib/hls/line_reader.c", "E:/Espressif/esp-adf/components/audio_stream/lib/hls/join_path.c", "E:/Espressif/esp-adf/components/audio_stream/i2s_stream_idf5.c", "E:/Espressif/esp-adf/components/audio_stream/lib/gzip/gzip_miniz.c", "E:/Espressif/esp-adf/components/audio_stream/aec_stream.c", "E:/Espressif/esp-adf/components/audio_stream/algorithm_stream.c", "E:/Espressif/esp-adf/components/audio_stream/tts_stream.c"], "include_dirs": ["include"]}, "battery_service": {"alias": "idf::battery_service", "target": "___idf_battery_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/battery_service", "type": "LIBRARY", "lib": "__idf_battery_service", "reqs": ["audio_sal", "esp_peripherals", "esp_timer"], "priv_reqs": ["esp_adc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/battery_service/libbattery_service.a", "sources": ["E:/Espressif/esp-adf/components/battery_service/battery_service.c", "E:/Espressif/esp-adf/components/battery_service/monitors/voltage_monitor.c"], "include_dirs": ["include", "./monitors/include"]}, "bluetooth_service": {"alias": "idf::bluetooth_service", "target": "___idf_bluetooth_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/bluetooth_service", "type": "LIBRARY", "lib": "__idf_bluetooth_service", "reqs": ["bt", "audio_sal", "audio_pipeline", "esp_peripherals", "audio_stream", "audio_hal"], "priv_reqs": ["nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/bluetooth_service/libbluetooth_service.a", "sources": ["E:/Espressif/esp-adf/components/bluetooth_service/bluetooth_service.c", "E:/Espressif/esp-adf/components/bluetooth_service/bt_keycontrol.c", "E:/Espressif/esp-adf/components/bluetooth_service/a2dp_stream.c", "E:/Espressif/esp-adf/components/bluetooth_service/hfp_stream.c"], "include_dirs": ["include"]}, "board": {"alias": "idf::board", "target": "___idf_board", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board", "type": "LIBRARY", "lib": "__idf_board", "reqs": [], "priv_reqs": ["audio_sal", "audio_hal", "esp_dispatcher", "esp_peripherals", "display_service"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/board/libboard.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board/board_ddclock/board.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board/board_ddclock/board_pins_config.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board/codec_driver/new_codec.c"], "include_dirs": ["./board_ddclock", "./codec_driver"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_mem.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_random.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/flash_encrypt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/secure_boot.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_utility.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/flash_partitions.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/esp_image_format.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/idf/bootloader_sha.c", "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support/src/esp32s3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "clouds": {"alias": "idf::clouds", "target": "___idf_clouds", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/clouds", "type": "CONFIG_ONLY", "lib": "__idf_clouds", "reqs": [], "priv_reqs": ["espressif__zlib"], "managed_reqs": [], "managed_priv_reqs": ["espressif__zlib"], "file": "", "sources": [], "include_dirs": ["./dueros/lightduer/include", "./volc_engine_rtc/include"]}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/cmock/libcmock.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/console/libconsole.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/console/commands.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/esp_console_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/split_argv.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/linenoise/linenoise.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/esp_console_repl_chip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_cmd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_date.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_dbl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_dstr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_end.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_file.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_hashtable.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_int.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_lit.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_rem.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_rex.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_str.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/arg_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/console/argtable3/argtable3.c"], "include_dirs": ["E:/Espressif/533/v5.3.3/esp-idf/components/console"]}, "coredump_upload_service": {"alias": "idf::coredump_upload_service", "target": "___idf_coredump_upload_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/coredump_upload_service", "type": "LIBRARY", "lib": "__idf_coredump_upload_service", "reqs": ["esp_http_client", "espcoredump", "spi_flash", "esp_rom"], "priv_reqs": ["audio_sal", "esp_dispatcher"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/coredump_upload_service/libcoredump_upload_service.a", "sources": ["E:/Espressif/esp-adf/components/coredump_upload_service/coredump_upload_service.c"], "include_dirs": ["include"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/cxx/libcxx.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/cxx/cxx_exception_stubs.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/cxx/cxx_guards.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/cxx/cxx_init.cpp"], "include_dirs": []}, "display_service": {"alias": "idf::display_service", "target": "___idf_display_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/display_service", "type": "LIBRARY", "lib": "__idf_display_service", "reqs": [], "priv_reqs": ["audio_sal", "audio_board", "audio_hal", "esp_peripherals"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/display_service/libdisplay_service.a", "sources": ["E:/Espressif/esp-adf/components/display_service/display_service.c", "E:/Espressif/esp-adf/components/display_service/led_indicator/led_indicator.c", "E:/Espressif/esp-adf/components/display_service/led_bar/led_bar_is31x.c", "E:/Espressif/esp-adf/components/display_service/led_bar/led_bar_aw2013.c", "E:/Espressif/esp-adf/components/display_service/led_bar/led_bar_ws2812.c"], "include_dirs": ["include", "led_indicator/include", "led_bar/include"]}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/driver/libdriver.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/adc_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/adc_dma_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/timer_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/i2c/i2c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/i2s_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/mcpwm_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/pcnt_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/rmt_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/sigma_delta_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/deprecated/rtc_temperature_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/touch_sensor/touch_sensor_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/touch_sensor/esp32s3/touch_sensor.c", "E:/Espressif/533/v5.3.3/esp-idf/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "drv_adc": {"alias": "idf::drv_adc", "target": "___idf_drv_adc", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_adc", "type": "LIBRARY", "lib": "__idf_drv_adc", "reqs": [], "priv_reqs": ["esp_adc", "hal_service", "board"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_adc/libdrv_adc.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_adc/drv_adc.c"], "include_dirs": ["include"]}, "drv_battery": {"alias": "idf::drv_battery", "target": "___idf_drv_battery", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_battery", "type": "LIBRARY", "lib": "__idf_drv_battery", "reqs": ["esp_adc", "board", "audio_sal", "battery_service", "utils", "drv_pca9557"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_battery/libdrv_battery.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_battery/drv_battery.c"], "include_dirs": ["include"]}, "drv_beep": {"alias": "idf::drv_beep", "target": "___idf_drv_beep", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_beep", "type": "LIBRARY", "lib": "__idf_drv_beep", "reqs": [], "priv_reqs": ["hal_service", "board", "drv_pca9557"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_beep/libdrv_beep.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_beep/drv_beep.c"], "include_dirs": ["include"]}, "drv_bt": {"alias": "idf::drv_bt", "target": "___idf_drv_bt", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_bt", "type": "LIBRARY", "lib": "__idf_drv_bt", "reqs": ["esp_driver_gpio", "board", "drv_pca9557"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_bt/libdrv_bt.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_bt/drv_bt.c"], "include_dirs": ["include"]}, "drv_encoder": {"alias": "idf::drv_encoder", "target": "___idf_drv_encoder", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_encoder", "type": "LIBRARY", "lib": "__idf_drv_encoder", "reqs": ["esp_driver_gpio", "esp_driver_pcnt", "board"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_encoder/libdrv_encoder.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_encoder/encoder.c"], "include_dirs": ["include"]}, "drv_htu21d": {"alias": "idf::drv_htu21d", "target": "___idf_drv_htu21d", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_htu21d", "type": "LIBRARY", "lib": "__idf_drv_htu21d", "reqs": ["esp_peripherals"], "priv_reqs": ["hal_i2c", "hal_service"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_htu21d/libdrv_htu21d.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_htu21d/drv_htu21d.c"], "include_dirs": ["include"]}, "drv_key": {"alias": "idf::drv_key", "target": "___idf_drv_key", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_key", "type": "LIBRARY", "lib": "__idf_drv_key", "reqs": [], "priv_reqs": ["esp_driver_gpio", "board", "drv_pca9557"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_key/libdrv_key.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_key/drv_key.c"], "include_dirs": ["include"]}, "drv_lcd_i80": {"alias": "idf::drv_lcd_i80", "target": "___idf_drv_lcd_i80", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_lcd_i80", "type": "LIBRARY", "lib": "__idf_drv_lcd_i80", "reqs": ["esp_lcd", "esp_driver_gpio", "board"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_lcd_i80/libdrv_lcd_i80.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_lcd_i80/lcd_i80.c"], "include_dirs": ["include"]}, "drv_pca9557": {"alias": "idf::drv_pca9557", "target": "___idf_drv_pca9557", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_pca9557", "type": "LIBRARY", "lib": "__idf_drv_pca9557", "reqs": [], "priv_reqs": ["hal_i2c", "board"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_pca9557/libdrv_pca9557.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_pca9557/drv_pca9557.c"], "include_dirs": ["include"]}, "drv_rtc": {"alias": "idf::drv_rtc", "target": "___idf_drv_rtc", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_rtc", "type": "LIBRARY", "lib": "__idf_drv_rtc", "reqs": ["hal_i2c", "hal_service"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/drv_rtc/libdrv_rtc.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_rtc/drv_rtc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_rtc/rtc_pcf8563.c"], "include_dirs": ["include"]}, "dueros_service": {"alias": "idf::dueros_service", "target": "___idf_dueros_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/dueros_service", "type": "LIBRARY", "lib": "__idf_dueros_service", "reqs": [], "priv_reqs": ["clouds", "mbedtls", "audio_board", "audio_hal", "esp-adf-libs", "audio_sal", "esp_peripherals", "audio_recorder", "wifi_service"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/dueros_service/libdueros_service.a", "sources": ["E:/Espressif/esp-adf/components/dueros_service/dueros_service.c", "E:/Espressif/esp-adf/components/dueros_service/esp_audio_device_info.c", "E:/Espressif/esp-adf/components/dueros_service/dueros_esp_flash.c", "E:/Espressif/esp-adf/components/dueros_service/duer_profile.c", "E:/Espressif/esp-adf/components/dueros_service/duer_wifi_cfg.c", "E:/Espressif/esp-adf/components/dueros_service/duer_wifi_cfg_dummy.c"], "include_dirs": ["include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/efuse/libefuse.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/efuse/esp32s3/esp_efuse_table.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/src/esp_efuse_api.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/src/esp_efuse_fields.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/src/esp_efuse_utility.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "E:/Espressif/533/v5.3.3/esp-idf/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp-adf-libs": {"alias": "idf::esp-adf-libs", "target": "___idf_esp-adf-libs", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp-adf-libs", "type": "LIBRARY", "lib": "__idf_esp-adf-libs", "reqs": ["audio_pipeline", "audio_sal", "espressif__nghttp", "esp-tls", "esp_netif"], "priv_reqs": ["espressif__nghttp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__nghttp"], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp-adf-libs/libesp-adf-libs.a", "sources": ["E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/audio_alc.c", "E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/audio_sonic.c", "E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/audio_forge.c", "E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/downmix.c", "E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/equalizer.c", "E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/filter_resample.c", "E:/Espressif/esp-adf/components/esp-adf-libs/esp_codec/wav_encoder.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_common.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_os.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/mem_trace/media_lib_mem_trace.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/mem_trace/media_lib_mem_his.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/port/media_lib_os_freertos.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_adapter.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_crypt.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/port/media_lib_crypt_default.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_socket.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/port/media_lib_socket_default.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_tls.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/port/media_lib_tls_default.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/media_lib_netif.c", "E:/Espressif/esp-adf/components/esp-adf-libs/media_lib_sal/port/media_lib_netif_default.c"], "include_dirs": ["./esp_h264/include", "./esp_audio/include", "./esp_codec/include/codec", "./esp_codec/include/processing", "./esp_new_jpeg/include", "./media_lib_sal/include", "./media_lib_sal/include/port", "./esp_muxer/include", "./esp_media_protocols/include"]}, "esp-ml307": {"alias": "idf::esp-ml307", "target": "___idf_esp-ml307", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307", "type": "LIBRARY", "lib": "__idf_esp-ml307", "reqs": ["esp_driver_gpio", "esp_driver_uart", "esp-tls", "esp_http_client", "mqtt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp-ml307/libesp-ml307.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/ml307_at_modem.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/ml307_ssl_transport.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/ml307_http.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/ml307_mqtt.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/ml307_udp.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/web_socket.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/tls_transport.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/tcp_transport.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/esp_http.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/esp_mqtt.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307/esp_udp.cc"], "include_dirs": ["include"]}, "esp-sr": {"alias": "idf::esp-sr", "target": "___idf_esp-sr", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp-sr", "type": "LIBRARY", "lib": "__idf_esp-sr", "reqs": ["json", "spiffs", "esp_partition"], "priv_reqs": ["spi_flash", "espressif__dl_fft", "espressif__esp-dsp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__dl_fft", "espressif__esp-dsp"], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp-sr/libesp-sr.a", "sources": ["E:/Espressif/esp-adf/components/esp-sr/src/model_path.c", "E:/Espressif/esp-adf/components/esp-sr/src/esp_sr_debug.c", "E:/Espressif/esp-adf/components/esp-sr/src/esp_mn_speech_commands.c", "E:/Espressif/esp-adf/components/esp-sr/src/esp_process_sdkconfig.c"], "include_dirs": ["esp-tts/esp_tts_chinese/include", "include/esp32s3", "src/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls/esp_tls.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls/esp_tls_error_capture.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls/esp_tls_platform_port.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_actions": {"alias": "idf::esp_actions", "target": "___idf_esp_actions", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_actions", "type": "LIBRARY", "lib": "__idf_esp_actions", "reqs": ["audio_sal", "nvs_flash", "spi_flash"], "priv_reqs": ["esp_dispatcher", "wifi_service", "display_service", "esp-adf-libs", "audio_pipeline", "audio_sal", "dueros_service", "audio_recorder"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_actions/libesp_actions.a", "sources": ["E:/Espressif/esp-adf/components/esp_actions/display_action.c", "E:/Espressif/esp-adf/components/esp_actions/dueros_action.c", "E:/Espressif/esp-adf/components/esp_actions/player_action.c", "E:/Espressif/esp-adf/components/esp_actions/recorder_action.c", "E:/Espressif/esp-adf/components/esp_actions/wifi_action.c", "E:/Espressif/esp-adf/components/esp_actions/nvs_action.c", "E:/Espressif/esp-adf/components/esp_actions/partition_action.c"], "include_dirs": ["include"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_oneshot.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_cali.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_cali_curve_fitting.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_continuous.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_monitor.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/gdma/adc_dma.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/adc_filter.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/esp32s3/curve_fitting_coefficients.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc/deprecated/esp32s3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_codec_dev": {"alias": "idf::esp_codec_dev", "target": "___idf_esp_codec_dev", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_codec_dev", "type": "LIBRARY", "lib": "__idf_esp_codec_dev", "reqs": ["driver"], "priv_reqs": ["freertos"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_codec_dev/libesp_codec_dev.a", "sources": ["E:/Espressif/esp-adf/components/esp_codec_dev/esp_codec_dev.c", "E:/Espressif/esp-adf/components/esp_codec_dev/esp_codec_dev_vol.c", "E:/Espressif/esp-adf/components/esp_codec_dev/esp_codec_dev_if.c", "E:/Espressif/esp-adf/components/esp_codec_dev/audio_codec_sw_vol.c", "E:/Espressif/esp-adf/components/esp_codec_dev/platform/audio_codec_gpio.c", "E:/Espressif/esp-adf/components/esp_codec_dev/platform/audio_codec_ctrl_i2c.c", "E:/Espressif/esp-adf/components/esp_codec_dev/platform/audio_codec_data_i2s.c", "E:/Espressif/esp-adf/components/esp_codec_dev/platform/audio_codec_ctrl_spi.c", "E:/Espressif/esp-adf/components/esp_codec_dev/platform/esp_codec_dev_os.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es8311/es8311.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es8156/es8156.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es7243e/es7243e.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es7210/es7210.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es7243/es7243.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es8388/es8388.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/tas5805m/tas5805m.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es8374/es8374.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/aw88298/aw88298.c", "E:/Espressif/esp-adf/components/esp_codec_dev/device/es8389/es8389.c"], "include_dirs": ["include", "interface", "device/include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex/esp32s3/esp_coex_adapter.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex/src/coexist_debug_diagram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_common/libesp_common.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_coze": {"alias": "idf::esp_coze", "target": "___idf_esp_coze", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_coze", "type": "LIBRARY", "lib": "__idf_esp_coze", "reqs": [], "priv_reqs": ["esp_http_client", "espressif__esp_websocket_client"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_websocket_client"], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_coze/libesp_coze.a", "sources": ["E:/Espressif/esp-adf/components/esp_coze/http_client_request.c"], "include_dirs": ["./include"]}, "esp_dispatcher": {"alias": "idf::esp_dispatcher", "target": "___idf_esp_dispatcher", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_dispatcher", "type": "LIBRARY", "lib": "__idf_esp_dispatcher", "reqs": [], "priv_reqs": ["audio_sal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_dispatcher/libesp_dispatcher.a", "sources": ["E:/Espressif/esp-adf/components/esp_dispatcher/audio_service.c", "E:/Espressif/esp-adf/components/esp_dispatcher/esp_dispatcher.c", "E:/Espressif/esp-adf/components/esp_dispatcher/periph_service.c", "E:/Espressif/esp-adf/components/esp_dispatcher/esp_delegate.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_cam/esp_cam_ctlr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio/src/gpio.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio/src/rtc_io.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio/src/dedic_gpio.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gptimer/src/gptimer.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c/i2c_master.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c/i2c_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_platform.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_std.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_pdm.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_tdm.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_com.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_fault.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_gen.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_oper.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_sync.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_encoder.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_rx.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdspi/src/sdspi_crc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdspi/src/sdspi_host.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_master.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_dma.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_uart/src/uart.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth/src/esp_eth.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth/src/esp_eth_netif_glue.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_event/libesp_event.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_event/default_event_loop.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_event/esp_event.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_event_cast": {"alias": "idf::esp_event_cast", "target": "___idf_esp_event_cast", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_event_cast", "type": "LIBRARY", "lib": "__idf_esp_event_cast", "reqs": ["audio_sal"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_event_cast/libesp_event_cast.a", "sources": ["E:/Espressif/esp-adf/components/esp_event_cast/esp_event_cast.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/src/gdbstub.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/src/gdbstub_transport.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/src/packet.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid/src/esp_hidd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid/src/esp_hidh.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client/esp_http_client.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client/lib/http_auth.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client/lib/http_header.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/httpd_main.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/httpd_parse.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/httpd_sess.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/httpd_txrx.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/httpd_uri.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/httpd_ws.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/cpu.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/esp_memory_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/esp_clk.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/clk_ctrl_os.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/hw_random.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/intr_alloc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/mac_addr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/periph_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/revision.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/rtc_module.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_modem.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_modes.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_console.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_usb.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_gpio.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_event.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/regi2c_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/esp_gpio_reserve.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sar_periph_ctrl_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/io_mux.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/esp_clk_tree.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp_clk_tree_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/dma/esp_dma_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/dma/gdma_link.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/spi_share_hw_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/spi_bus_lock.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/clk_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/adc_share_hw_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/dma/gdma.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/deprecated/gdma_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/dma/esp_async_memcpy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/dma/async_memcpy_gdma.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/systimer.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/esp_hmac.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/esp_ds.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/mspi_timing_tuning.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/mspi_timing_by_mspi_delay.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/sleep_wake_stub.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/esp_clock_output.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/esp_crypto_lock.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/sar_periph_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/mspi_timing_config.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp32s3/esp_memprot.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/port/esp_memprot_conv.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support/lowpower/cpu_retention/port/esp32s3/sleep_cpu.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_io.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_st7789.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_ops.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/i80/esp_lcd_panel_io_i80.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd/rgb/esp_lcd_panel_rgb.c"], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm/esp_mmu_map.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm/port/esp32s3/ext_mem_layout.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm/esp_cache.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_mpg123": {"alias": "idf::esp_mpg123", "target": "___idf_esp_mpg123", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_mpg123", "type": "LIBRARY", "lib": "__idf_esp_mpg123", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_mpg123/libesp_mpg123.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_mpg123/mpg123"], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_mpg123/mpg123/src/include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/esp_netif_handlers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/esp_netif_objects.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/esp_netif_defaults.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/lwip/esp_netif_lwip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/lwip/esp_netif_sntp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/lwip/netif/wlanif.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/lwip/netif/ethernetif.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_ogg": {"alias": "idf::esp_ogg", "target": "___idf_esp_ogg", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_ogg", "type": "LIBRARY", "lib": "__idf_esp_ogg", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_ogg/libesp_ogg.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_ogg/ogg"], "include_dirs": ["ogg"]}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "bootloader_support", "spi_flash", "app_update", "partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_partition/partition.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_peripherals": {"alias": "idf::esp_peripherals", "target": "___idf_esp_peripherals", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_peripherals", "type": "LIBRARY", "lib": "__idf_esp_peripherals", "reqs": ["driver", "audio_hal", "audio_sal", "fatfs", "console", "audio_pipeline", "audio_board", "spiffs", "display_service", "esp_dispatcher", "bt", "mbedtls", "wpa_supplicant", "nvs_flash", "esp_lcd", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_peripherals/libesp_peripherals.a", "sources": ["E:/Espressif/esp-adf/components/esp_peripherals/esp_peripherals.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_adc_button.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_button.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_console.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_gpio_isr.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_is31fl3216.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_led.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_spiffs.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_wifi.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_aw2013.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_ws2812.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_lcd.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/button/button.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/blufi/blufi_security.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/blufi/wifibleconfig.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/adc_button/adc_button.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/IS31FL3216/IS31FL3216.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/tca9554/tca9554.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/gpio_isr/gpio_isr.c", "E:/Espressif/esp-adf/components/esp_peripherals/driver/i2c_bus/i2c_bus_v2.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/sdcard/sdcard.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_sdcard.c", "E:/Espressif/esp-adf/components/esp_peripherals/periph_touch.c", "E:/Espressif/esp-adf/components/esp_peripherals/lib/touch/touch.c"], "include_dirs": ["./include", "./lib/adc_button", "./lib/gpio_isr", "./lib/button", "./lib/blufi", "./lib/IS31FL3216", "./lib/aw2013", "./lib/tca9554", "./driver/i2c_bus", "./lib/sdcard", "./lib/touch"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/src/phy_override.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/src/lib_printf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/src/phy_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/src/phy_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/esp32s3/phy_init_data.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm/pm_locks.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm/pm_trace.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram", "type": "LIBRARY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_psram/libesp_psram.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram/esp_psram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram/mmu_psram_flash.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram/esp32s3/esp_psram_impl_octal.c"], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rlottie": {"alias": "idf::esp_rlottie", "target": "___idf_esp_rlottie", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_rlottie", "type": "CONFIG_ONLY", "lib": "__idf_esp_rlottie", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_rlottie/rlottie/inc"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_systimer.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_wdt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"], "include_dirs": ["include", "include/esp32s3", "esp32s3"]}, "esp_simple_dsp": {"alias": "idf::esp_simple_dsp", "target": "___idf_esp_simple_dsp", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_simple_dsp", "type": "CONFIG_ONLY", "lib": "__idf_esp_simple_dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["."]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_system/libesp_system.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/esp_err.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/crosscore_int.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/esp_ipc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/freertos_hooks.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/int_wdt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/panic.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/esp_system.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/startup.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/startup_funcs.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/system_time.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/stack_check.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/ubsan.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/xt_wdt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/task_wdt/task_wdt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/cpu_start.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/panic_handler.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/esp_system_chip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/image_process.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/brownout.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/esp_ipc_isr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/panic_arch.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/debug_stubs.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/arch/xtensa/trax.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/soc/esp32s3/highint_hdl.S", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/soc/esp32s3/clk.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/soc/esp32s3/reset_reason.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/soc/esp32s3/system_internal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/soc/esp32s3/cache_err_int.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system/port/soc/esp32s3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/src/esp_timer.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/src/esp_timer_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/src/ets_timer_legacy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/src/system_time.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/src/esp_timer_impl_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_vorbis": {"alias": "idf::esp_vorbis", "target": "___idf_esp_vorbis", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis", "type": "LIBRARY", "lib": "__idf_esp_vorbis", "reqs": ["esp_ogg"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_vorbis/libesp_vorbis.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis/vorbis/lib/o_vorbisenc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis/vorbis/lib/o_vorbisfile.c"], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis/vorbis/include", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis/vorbis/lib"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/lib_printf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/mesh_event.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/smartconfig.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/wifi_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/wifi_default.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/wifi_netif.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/wifi_default_ap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/esp32s3/esp_adapter.c", "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_flash.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_uart.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_elf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_binary.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_sha.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/core_dump_crc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "espressif__cmake_utilities": {"alias": "idf::espressif__cmake_utilities", "target": "___idf_espressif__cmake_utilities", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__cmake_utilities", "type": "CONFIG_ONLY", "lib": "__idf_espressif__cmake_utilities", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "espressif__dl_fft": {"alias": "idf::espressif__dl_fft", "target": "___idf_espressif__dl_fft", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft", "type": "LIBRARY", "lib": "__idf_espressif__dl_fft", "reqs": ["espressif__esp-dsp"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espressif__dl_fft/libespressif__dl_fft.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/dl_fft_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/dl_fft_s16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/dl_rfft_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/dl_rfft_s16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/base/dl_fft2r_fc32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/base/dl_fft4r_fc32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/base/dl_fft2r_sc16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/base/dl_fft_base.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/base/isa/esp32s3/dl_fft2r_fc32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft/base/isa/esp32s3/dl_fft4r_fc32_aes3.S"], "include_dirs": [".", "base", "base/isa"]}, "espressif__esp-dsp": {"alias": "idf::espressif__esp-dsp", "target": "___idf_espressif__esp-dsp", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp", "type": "LIBRARY", "lib": "__idf_espressif__esp-dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espressif__esp-dsp/libespressif__esp-dsp.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/common/misc/dsps_pwroftwo.cpp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/common/misc/aes3_tie_log.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_m_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_m_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_m_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dspi_dotprod_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/float/dspi_dotprod_off_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_3x3x1_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_3x3x3_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_4x4x1_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_4x4x4_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_m_ae32_vector.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_m_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/add/float/dspm_add_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/add/float/dspm_add_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/addc/float/dspm_addc_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/addc/float/dspm_addc_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mulc/float/dspm_mulc_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mulc/float/dspm_mulc_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/sub/float/dspm_sub_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/sub/float/dspm_sub_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/matrix/mat/mat.cpp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mulc/float/dsps_mulc_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/addc/float/dsps_addc_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mulc/fixed/dsps_mulc_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mulc/fixed/dsps_mulc_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/float/dsps_add_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/float/dsps_sub_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/float/dsps_mul_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s8_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s8_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mulc/float/dsps_mulc_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/addc/float/dsps_addc_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/add/float/dsps_add_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sub/float/dsps_sub_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/mul/float/dsps_mul_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/math/sqrt/float/dsps_sqrt_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ae32_.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_aes3_.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ae32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_bit_rev_lookup_fc32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ae32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ae32_.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_aes3_.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_bitrev_tables_fc32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_bitrev_tables_fc32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dct_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dctiv_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dstiv_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/snr/float/dsps_snr_f32.cpp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/sfdr/float/dsps_sfdr_f32.cpp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/misc/dsps_d_gen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/misc/dsps_h_gen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/misc/dsps_tone_gen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen_init.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/mem/esp32s3/dsps_memset_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/mem/esp32s3/dsps_memcpy_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/support/view/dsps_view.cpp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/windows/hann/float/dsps_wind_hann_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/windows/blackman/float/dsps_wind_blackman_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/windows/blackman_harris/float/dsps_wind_blackman_harris_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/windows/blackman_nuttall/float/dsps_wind_blackman_nuttall_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/windows/nuttall/float/dsps_wind_nuttall_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/windows/flat_top/float/dsps_wind_flat_top_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dsps_conv_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dspi_conv_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dsps_conv_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dsps_corr_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dsps_corr_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dsps_ccorr_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/conv/float/dsps_ccorr_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_gen_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_init_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_init_f32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_init_s16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_ansi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fir_s16_m_ae32.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_aes3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_arp4.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/kalman/ekf/common/ekf.cpp", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp/modules/kalman/ekf_imu13states/ekf_imu13states.cpp"], "include_dirs": ["modules/dotprod/include", "modules/support/include", "modules/support/mem/include", "modules/windows/include", "modules/windows/hann/include", "modules/windows/blackman/include", "modules/windows/blackman_harris/include", "modules/windows/blackman_nuttall/include", "modules/windows/nuttall/include", "modules/windows/flat_top/include", "modules/iir/include", "modules/fir/include", "modules/math/include", "modules/math/add/include", "modules/math/sub/include", "modules/math/mul/include", "modules/math/addc/include", "modules/math/mulc/include", "modules/math/sqrt/include", "modules/matrix/mul/include", "modules/matrix/add/include", "modules/matrix/addc/include", "modules/matrix/mulc/include", "modules/matrix/sub/include", "modules/matrix/include", "modules/fft/include", "modules/dct/include", "modules/conv/include", "modules/common/include", "modules/matrix/mul/test/include", "modules/kalman/ekf/include", "modules/kalman/ekf_imu13states/include"]}, "espressif__esp_lcd_ili9341": {"alias": "idf::espressif__esp_lcd_ili9341", "target": "___idf_espressif__esp_lcd_ili9341", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_lcd_ili9341", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_ili9341", "reqs": ["driver", "esp_lcd"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espressif__esp_lcd_ili9341/libespressif__esp_lcd_ili9341.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_lcd_ili9341/esp_lcd_ili9341.c"], "include_dirs": ["include"]}, "espressif__esp_websocket_client": {"alias": "idf::espressif__esp_websocket_client", "target": "___idf_espressif__esp_websocket_client", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_websocket_client", "type": "LIBRARY", "lib": "__idf_espressif__esp_websocket_client", "reqs": ["lwip", "esp-tls", "tcp_transport", "http_parser", "esp_event"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espressif__esp_websocket_client/libespressif__esp_websocket_client.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_websocket_client/esp_websocket_client.c"], "include_dirs": ["include"]}, "espressif__jsmn": {"alias": "idf::espressif__jsmn", "target": "___idf_espressif__jsmn", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__jsmn", "type": "CONFIG_ONLY", "lib": "__idf_espressif__jsmn", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "espressif__nghttp": {"alias": "idf::espressif__nghttp", "target": "___idf_espressif__nghttp", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp", "type": "LIBRARY", "lib": "__idf_espressif__nghttp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espressif__nghttp/libespressif__nghttp.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_buf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_callbacks.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_debug.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_extpri.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_frame.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_hd.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_hd_huffman.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_hd_huffman_data.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_helper.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_http.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_map.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_mem.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_alpn.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_option.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_outbound_item.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_pq.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_priority_spec.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_queue.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_ratelim.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_rcbuf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_session.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_stream.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_submit.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_time.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/nghttp2_version.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp/nghttp2/lib/sfparse.c"], "include_dirs": ["port/include", "nghttp2/lib/includes"]}, "espressif__zlib": {"alias": "idf::espressif__zlib", "target": "___idf_espressif__zlib", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib", "type": "LIBRARY", "lib": "__idf_espressif__zlib", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/espressif__zlib/libespressif__zlib.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/adler32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/compress.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/crc32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/deflate.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/gzclose.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/gzlib.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/gzread.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/gzwrite.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/infback.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/inffast.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/inflate.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/inftrees.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/trees.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/uncompr.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib/zlib/zutil.c"], "include_dirs": ["zlib"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/fatfs/libfatfs.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/diskio/diskio.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/diskio/diskio_rawflash.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/diskio/diskio_wl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/src/ff.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/src/ffunicode.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/port/freertos/ffsystem.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/diskio/diskio_sdmmc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/vfs/vfs_fat.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/vfs/vfs_fat_sdmmc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/freertos/libfreertos.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/freertos/heap_idf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/app_startup.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/port_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/port_systick.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/list.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/queue.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/tasks.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/timers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/event_groups.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/esp_additions/freertos_compatibility.c", "E:/Espressif/533/v5.3.3/esp-idf/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "gzip_miniz": {"alias": "idf::gzip_miniz", "target": "___idf_gzip_miniz", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/gzip_miniz", "type": "LIBRARY", "lib": "__idf_gzip_miniz", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/gzip_miniz/libgzip_miniz.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/gzip_miniz/gzip_miniz.c"], "include_dirs": [".", "include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/hal/libhal.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/hal/hal_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/mpu_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/efuse_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/esp32s3/efuse_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/mmu_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/cache_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/color_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_flash_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_flash_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_flash_encrypt_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/esp32s3/clk_tree_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/systimer_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/uart_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/uart_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/gpio_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/rtc_io_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/timer_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/ledc_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/ledc_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/i2c_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/i2c_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/rmt_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/pcnt_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/mcpwm_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/twai_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/twai_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/gdma_hal_top.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/gdma_hal_ahb_v1.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/i2s_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/sdm_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/sdmmc_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/adc_hal_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/adc_oneshot_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/adc_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/lcd_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/mpi_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/sha_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/aes_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/brownout_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_slave_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_slave_hal_iram.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_slave_hd_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/spi_flash_hal_gpspi.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/hmac_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/ds_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/usb_serial_jtag_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/usb_dwc_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/usb_wrap_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/esp32s3/touch_sensor_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/touch_sensor_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/xt_wdt_hal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/hal/esp32s3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "hal_i2c": {"alias": "idf::hal_i2c", "target": "___idf_hal_i2c", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_i2c", "type": "LIBRARY", "lib": "__idf_hal_i2c", "reqs": ["board", "audio_sal", "esp_peripherals", "esp_driver_gpio"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/hal_i2c/libhal_i2c.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_i2c/hal_i2c.c"], "include_dirs": ["include"]}, "hal_service": {"alias": "idf::hal_service", "target": "___idf_hal_service", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_service", "type": "LIBRARY", "lib": "__idf_hal_service", "reqs": ["board", "audio_sal", "esp_peripherals"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/hal_service/libhal_service.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_service/hal_service.c"], "include_dirs": ["include"]}, "hal_wifi": {"alias": "idf::hal_wifi", "target": "___idf_hal_wifi", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_wifi", "type": "LIBRARY", "lib": "__idf_hal_wifi", "reqs": ["hal_service", "json", "esp_http_server", "esp_http_client"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/hal_wifi/libhal_wifi.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_wifi/hal_wifi.c"], "include_dirs": ["include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/heap/libheap.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/heap/heap_caps_base.c", "E:/Espressif/533/v5.3.3/esp-idf/components/heap/heap_caps.c", "E:/Espressif/533/v5.3.3/esp-idf/components/heap/heap_caps_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/heap/multi_heap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/heap/tlsf/tlsf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/heap/port/memory_layout_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/heap/port/esp32s3/memory_layout.c"], "include_dirs": ["include"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_http_rest_client": {"alias": "idf::idf_http_rest_client", "target": "___idf_idf_http_rest_client", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client", "type": "LIBRARY", "lib": "__idf_idf_http_rest_client", "reqs": ["esp_http_client", "esp-tls", "json"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/idf_http_rest_client/libidf_http_rest_client.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client/src/http_event_handler.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client/src/http_rest_client.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client/src/http_rest_json_client.c"], "include_dirs": ["./include"]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "input_key_service": {"alias": "idf::input_key_service", "target": "___idf_input_key_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/input_key_service", "type": "LIBRARY", "lib": "__idf_input_key_service", "reqs": [], "priv_reqs": ["audio_sal", "esp_peripherals"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/input_key_service/libinput_key_service.a", "sources": ["E:/Espressif/esp-adf/components/input_key_service/input_key_service.c"], "include_dirs": ["./include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/json/libjson.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/json/cJSON/cJSON.c", "E:/Espressif/533/v5.3.3/esp-idf/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "list": {"alias": "idf::list", "target": "___idf_list", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/list", "type": "LIBRARY", "lib": "__idf_list", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/list/liblist.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/list/list.c"], "include_dirs": ["."]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/log/liblog.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/log/log.c", "E:/Espressif/533/v5.3.3/esp-idf/components/log/log_buffers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/log/log_freertos.c"], "include_dirs": ["include"]}, "lvgl": {"alias": "idf::lvgl", "target": "___idf_lvgl", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl", "type": "LIBRARY", "lib": "__idf_lvgl", "reqs": ["esp_timer", "esp_rlottie", "fatfs"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/lvgl/liblvgl.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_disp.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_event.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_group.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_indev.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_indev_scroll.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_class.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_draw.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_pos.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_scroll.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_style.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_style_gen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_obj_tree.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_refr.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/core/lv_theme.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/arm2d/lv_gpu_arm2d.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_arc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_img.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_label.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_layer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_line.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_mask.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_rect.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_transform.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_draw_triangle.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_img_buf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_img_cache.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/lv_img_decoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/pxp/lv_draw_pxp_blend.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp_osa.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_blend.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_rect.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/renesas/lv_gpu_d2_draw_label.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/renesas/lv_gpu_d2_ra6m3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_arc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_bg.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_composite.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_img.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_label.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_layer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_line.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_mask.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_polygon.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_rect.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_stack_blur.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_texture_cache.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sdl/lv_draw_sdl_utils.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_arc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_blend.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_dither.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_gradient.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_img.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_layer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_letter.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_line.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_polygon.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_rect.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/sw/lv_draw_sw_transform.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/layouts/flex/lv_flex.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/layouts/grid/lv_grid.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/bmp/lv_bmp.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/ffmpeg/lv_ffmpeg.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/freetype/lv_freetype.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/fsdrv/lv_fs_fatfs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/fsdrv/lv_fs_littlefs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/fsdrv/lv_fs_posix.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/fsdrv/lv_fs_stdio.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/fsdrv/lv_fs_win32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/gif/gifdec.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/gif/lv_gif.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/png/lodepng.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/png/lv_png.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/qrcode/lv_qrcode.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/qrcode/qrcodegen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/rlottie/lv_rlottie.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/sjpg/lv_sjpg.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/sjpg/tjpgd.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/libs/tiny_ttf/lv_tiny_ttf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/lv_extra.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/fragment/lv_fragment.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/fragment/lv_fragment_manager.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/gridnav/lv_gridnav.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/ime/lv_ime_pinyin.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/imgfont/lv_imgfont.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/monkey/lv_monkey.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/msg/lv_msg.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/others/snapshot/lv_snapshot.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/themes/basic/lv_theme_basic.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/themes/default/lv_theme_default.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/themes/mono/lv_theme_mono.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/animimg/lv_animimg.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/calendar/lv_calendar.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/calendar/lv_calendar_header_arrow.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/calendar/lv_calendar_header_dropdown.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/chart/lv_chart.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/colorwheel/lv_colorwheel.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/imgbtn/lv_imgbtn.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/keyboard/lv_keyboard.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/led/lv_led.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/list/lv_list.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/menu/lv_menu.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/meter/lv_meter.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/msgbox/lv_msgbox.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/span/lv_span.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/spinbox/lv_spinbox.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/spinner/lv_spinner.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/tabview/lv_tabview.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/tileview/lv_tileview.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/extra/widgets/win/lv_win.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_fmt_txt.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_loader.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_10.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_12.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_12_subpx.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_14.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_18.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_22.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_24.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_26.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_28.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_28_compressed.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_30.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_34.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_36.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_38.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_40.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_42.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_44.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_46.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_montserrat_8.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_simsun_16_cjk.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_unscii_16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/font/lv_font_unscii_8.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/hal/lv_hal_disp.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/hal/lv_hal_indev.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/hal/lv_hal_tick.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_anim.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_anim_timeline.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_area.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_async.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_bidi.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_color.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_fs.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_gc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_ll.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_log.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_lru.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_math.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_mem.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_printf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_style.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_style_gen.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_templ.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_timer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_tlsf.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_txt.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_txt_ap.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/misc/lv_utils.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_arc.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_bar.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_btn.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_btnmatrix.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_canvas.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_checkbox.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_dropdown.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_img.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_label.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_line.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_objx_templ.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_roller.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_slider.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_switch.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_table.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src/widgets/lv_textarea.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/anim/lv_example_anim_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/anim/lv_example_anim_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/anim/lv_example_anim_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/anim/lv_example_anim_timeline_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/animimg001.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/animimg002.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/animimg003.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/emoji/img_emoji_F617.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_caret_down.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_cogwheel_alpha16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_cogwheel_argb.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_cogwheel_chroma_keyed.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_cogwheel_indexed16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_cogwheel_rgb.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_hand.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_skew_strip.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/img_star.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/imgbtn_left.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/imgbtn_mid.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/assets/imgbtn_right.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/event/lv_example_event_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/event/lv_example_event_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/event/lv_example_event_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/event/lv_example_event_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/get_started/lv_example_get_started_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/get_started/lv_example_get_started_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/get_started/lv_example_get_started_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/flex/lv_example_flex_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/flex/lv_example_flex_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/flex/lv_example_flex_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/flex/lv_example_flex_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/flex/lv_example_flex_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/flex/lv_example_flex_6.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/grid/lv_example_grid_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/grid/lv_example_grid_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/grid/lv_example_grid_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/grid/lv_example_grid_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/grid/lv_example_grid_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/layouts/grid/lv_example_grid_6.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/bmp/lv_example_bmp_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/freetype/lv_example_freetype_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/gif/img_bulb_gif.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/gif/lv_example_gif_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/png/img_wink_png.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/png/lv_example_png_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/qrcode/lv_example_qrcode_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/rlottie/lv_example_rlottie_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/rlottie/lv_example_rlottie_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/rlottie/lv_example_rlottie_approve.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/libs/sjpg/lv_example_sjpg_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/fragment/lv_example_fragment_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/fragment/lv_example_fragment_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/gridnav/lv_example_gridnav_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/gridnav/lv_example_gridnav_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/gridnav/lv_example_gridnav_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/gridnav/lv_example_gridnav_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/ime/lv_example_ime_pinyin_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/ime/lv_example_ime_pinyin_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/imgfont/lv_example_imgfont_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/monkey/lv_example_monkey_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/monkey/lv_example_monkey_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/monkey/lv_example_monkey_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/msg/lv_example_msg_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/msg/lv_example_msg_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/msg/lv_example_msg_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/others/snapshot/lv_example_snapshot_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/porting/lv_port_disp_template.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/porting/lv_port_fs_template.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/porting/lv_port_indev_template.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/scroll/lv_example_scroll_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/scroll/lv_example_scroll_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/scroll/lv_example_scroll_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/scroll/lv_example_scroll_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/scroll/lv_example_scroll_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/scroll/lv_example_scroll_6.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_10.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_11.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_12.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_13.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_14.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_15.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_6.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_7.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_8.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/styles/lv_example_style_9.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/animimg/lv_example_animimg_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/arc/lv_example_arc_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/arc/lv_example_arc_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/bar/lv_example_bar_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/bar/lv_example_bar_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/bar/lv_example_bar_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/bar/lv_example_bar_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/bar/lv_example_bar_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/bar/lv_example_bar_6.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/btn/lv_example_btn_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/btn/lv_example_btn_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/btn/lv_example_btn_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/calendar/lv_example_calendar_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/canvas/lv_example_canvas_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/canvas/lv_example_canvas_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_6.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_7.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_8.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/chart/lv_example_chart_9.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/checkbox/lv_example_checkbox_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/checkbox/lv_example_checkbox_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/colorwheel/lv_example_colorwheel_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/dropdown/lv_example_dropdown_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/dropdown/lv_example_dropdown_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/dropdown/lv_example_dropdown_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/img/lv_example_img_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/img/lv_example_img_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/img/lv_example_img_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/img/lv_example_img_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/imgbtn/lv_example_imgbtn_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/keyboard/lv_example_keyboard_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/label/lv_example_label_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/label/lv_example_label_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/label/lv_example_label_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/label/lv_example_label_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/label/lv_example_label_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/led/lv_example_led_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/line/lv_example_line_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/list/lv_example_list_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/list/lv_example_list_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/menu/lv_example_menu_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/menu/lv_example_menu_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/menu/lv_example_menu_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/menu/lv_example_menu_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/menu/lv_example_menu_5.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/meter/lv_example_meter_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/meter/lv_example_meter_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/meter/lv_example_meter_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/meter/lv_example_meter_4.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/msgbox/lv_example_msgbox_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/obj/lv_example_obj_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/obj/lv_example_obj_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/roller/lv_example_roller_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/roller/lv_example_roller_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/roller/lv_example_roller_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/slider/lv_example_slider_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/slider/lv_example_slider_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/slider/lv_example_slider_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/span/lv_example_span_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/spinbox/lv_example_spinbox_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/spinner/lv_example_spinner_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/switch/lv_example_switch_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/table/lv_example_table_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/table/lv_example_table_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/tabview/lv_example_tabview_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/tabview/lv_example_tabview_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/textarea/lv_example_textarea_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/textarea/lv_example_textarea_2.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/textarea/lv_example_textarea_3.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/tileview/lv_example_tileview_1.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples/widgets/win/lv_example_win_1.c"], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/../", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/demos"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/lwip/liblwip.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/lwip/apps/sntp/sntp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/api_lib.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/api_msg.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/err.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/if_api.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/netbuf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/netdb.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/netifapi.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/sockets.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/api/tcpip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/apps/sntp/sntp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/def.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/dns.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/inet_chksum.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/mem.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/memp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/netif.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/pbuf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/raw.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/stats.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/sys.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/tcp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/tcp_in.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/tcp_out.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/timeouts.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/udp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/autoip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/dhcp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/etharp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/icmp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/igmp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/dhcp6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ethip6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/icmp6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/inet6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ip6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/mld6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/nd6.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ethernet.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/bridgeif.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/bridgeif_fdb.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/slipif.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/auth.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ccp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/chap-md5.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/chap-new.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/chap_ms.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/demand.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/eap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ecp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/eui64.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/fsm.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ipcp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/lcp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/magic.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/mppe.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/multilink.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ppp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppapi.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppoe.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppos.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/upap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/vj.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/port/hooks/tcp_isn_default.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/port/hooks/lwip_default_hooks.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/port/debug/lwip_debug.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/port/sockets_ext.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/port/freertos/sys_arch.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/port/esp32xx/vfs_lwip.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/apps/ping/esp_ping.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/apps/ping/ping.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/apps/ping/ping_sock.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "E:/Espressif/533/v5.3.3/esp-idf/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": ["78__esp-opus", "espressif__esp_websocket_client"], "managed_reqs": [], "managed_priv_reqs": ["78__esp-opus", "espressif__esp_websocket_client"], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/main/libmain.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/config/dev_config.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/gui_port/gui_port.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/gui_port/lv_port_disp.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/gui_port/lv_port_indev.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/custom/custom.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/fonts/font_harmonyos_sans_sc_14.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/fonts/font_weather_32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/events_init.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/gui_guider.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_Acme_Regular_12.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_Acme_Regular_16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_Alatsi_Regular_18.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_Alatsi_Regular_32.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_Antonio_Regular_18.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_D_DIN_PRO_700_Bold_12.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_D_DIN_PRO_700_Bold_14.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_D_DIN_PRO_700_Bold_16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_D_DIN_PRO_700_Bold_18.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_HarmonyOS_Sans_SC_Regular_12.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_HarmonyOS_Sans_SC_Regular_14.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_HarmonyOS_Sans_SC_Regular_24.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_montserratMedium_12.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_montserratMedium_14.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_montserratMedium_16.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_montserratMedium_18.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_montserratMedium_22.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/guider_fonts/lv_font_montserratMedium_24.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icom_smile_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_alarmclock_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_calendar_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_chat_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_cpu_alpha_64x64.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_file_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_gpu_alpha_64x64.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_home_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_ht_alpha_24x24.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_humidity_alpha_18x18.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_i2c_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_mini_0_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_music_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_pc_monitor_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_player_alpha_64x64.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_ram_alpha_64x64.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_recorder_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_about_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_ai_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_back_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_bkl_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_reboot_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_reset_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_sleep_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_sound_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_time_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_webserver_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_setting_wifi_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_sunrise_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_sunset_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_timer_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_update_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_uv_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_icon_weather_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/images/_neutral_48_alpha_48x48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_alarmclock.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_calendar.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_chat.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_file.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_i2c.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_logo.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_main.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_menu.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_music.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_pc_monitor.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_recorder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_about.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_ai.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_bkl.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_network.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_sleep.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_sound.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_time.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_setting_webserver.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_timer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_update.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/setup_scr_screen_weather.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated/widgets_init.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_0_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_1_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_2_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_3_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_4_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_5_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_6_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_7_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_8_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_mini_9_alpha_70x92.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_moonrise_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_icon_moonset_alpha_20x20.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/_wooden_fish_mini_alpha_80x80.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/angry_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/confident_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/confused_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/cool_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/crying_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/delicious_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/embarrassed_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/funny_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/happy_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/kissy_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/laughing_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/loving_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/mergeBinFile.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/neutral_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/relaxed_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/sad_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/shocked_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/silly_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/sleepy_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/surprised_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/thinking_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images/emoji/winking_48.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc/ui_file_manager.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc/ui_flipnum.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc/ui_keyboard.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc/ui_keyboard_helper.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc/ui_toast_txt.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/ui_notif_service.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/ui_scr_def.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/ui_scr_manager.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_calendar.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_chat.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_file.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_layertop.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_main.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_music.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_network.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_pc_monitor.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_recorder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_setting_time.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_setting_webserver.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_timer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_update.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_notif_scr_weather.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_alarmlcock.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_calendar.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_chat.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_file.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_i2c.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_logo.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_main.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_menu.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_music.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_pc_monitor.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_recorder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_about.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_ai.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_bkl.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_network.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_sleep.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_sound.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_time.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_setting_webserver.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_templete.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_timer.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_update.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user/ui_setup_scr_weather.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/misc/select_sw.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/alarm_clock.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/main.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/ota_webserver.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/pc_monitor_client.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/periph_manager.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/player_control.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/sys_sleep.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/timer_task.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/web_server.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/xzai_handler.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/index_wifi_config.html.gz.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/index_ota_update.html.gz.S"], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/include", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/gui_port", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/custom", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/misc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/config", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/www"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/mqtt/libmqtt.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/mqtt_client.c", "E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/newlib/libnewlib.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/newlib/abort.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/assert.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/heap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/locks.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/poll.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/pthread.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/random.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/getentropy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/reent_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/newlib_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/syscalls.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/termios.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/stdatomic.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/time.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/sysconf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/realpath.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/scandir.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/port/xtensa/stdatomic_s32c1i.c", "E:/Espressif/533/v5.3.3/esp-idf/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_api.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_cxx_api.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_item_hash_list.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_page.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_pagemanager.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_storage.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_handle_simple.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_handle_locked.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_partition.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_partition_lookup.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_partition_manager.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_types.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_platform.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include", "../spi_flash/include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "ota_service": {"alias": "idf::ota_service", "target": "___idf_ota_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/ota_service", "type": "LIBRARY", "lib": "__idf_ota_service", "reqs": ["app_update", "esp_https_ota", "esp_app_format"], "priv_reqs": ["esp_peripherals", "audio_pipeline", "audio_sal", "audio_stream"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/ota_service/libota_service.a", "sources": ["E:/Espressif/esp-adf/components/ota_service/esp_fs_ota.c", "E:/Espressif/esp-adf/components/ota_service/ota_service.c", "E:/Espressif/esp-adf/components/ota_service/ota_proc_default.c"], "include_dirs": ["include"]}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/perfmon/libperfmon.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/perfmon/xtensa_perfmon_access.c", "E:/Espressif/533/v5.3.3/esp-idf/components/perfmon/xtensa_perfmon_apis.c", "E:/Espressif/533/v5.3.3/esp-idf/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "player": {"alias": "idf::player", "target": "___idf_player", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/player", "type": "LIBRARY", "lib": "__idf_player", "reqs": [], "priv_reqs": ["board", "audio_sal", "audio_hal", "audio_stream", "esp_dispatcher", "esp_peripherals", "display_service", "hal_service", "esp_vorbis", "esp_mpg123", "list", "utils"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/player/libplayer.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/player/player.c"], "include_dirs": ["include"]}, "playlist": {"alias": "idf::playlist", "target": "___idf_playlist", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/playlist", "type": "LIBRARY", "lib": "__idf_playlist", "reqs": ["audio_sal", "nvs_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/playlist/libplaylist.a", "sources": ["E:/Espressif/esp-adf/components/playlist/playlist.c", "E:/Espressif/esp-adf/components/playlist/playlist_operator/dram_list.c", "E:/Espressif/esp-adf/components/playlist/playlist_operator/flash_list.c", "E:/Espressif/esp-adf/components/playlist/playlist_operator/partition_list.c", "E:/Espressif/esp-adf/components/playlist/playlist_operator/sdcard_list.c", "E:/Espressif/esp-adf/components/playlist/sdcard_scan/sdcard_scan.c"], "include_dirs": ["./include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/protocomm/libprotocomm.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/common/protocomm.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/proto-c/constants.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/proto-c/sec0.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/proto-c/sec1.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/proto-c/sec2.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/proto-c/session.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/transports/protocomm_console.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/transports/protocomm_httpd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/security/security0.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/security/security1.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/security/security2.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/pthread/libpthread.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/pthread/pthread.c", "E:/Espressif/533/v5.3.3/esp-idf/components/pthread/pthread_cond_var.c", "E:/Espressif/533/v5.3.3/esp-idf/components/pthread/pthread_local_storage.c", "E:/Espressif/533/v5.3.3/esp-idf/components/pthread/pthread_rwlock.c", "E:/Espressif/533/v5.3.3/esp-idf/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "qweather": {"alias": "idf::qweather", "target": "___idf_qweather", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/qweather", "type": "LIBRARY", "lib": "__idf_qweather", "reqs": [], "priv_reqs": ["hal_service", "hal_wifi", "idf_http_rest_client", "gzip_miniz"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/qweather/libqweather.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/qweather/qweather.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/qweather/qweather_ca.c"], "include_dirs": ["include"]}, "recorder": {"alias": "idf::recorder", "target": "___idf_recorder", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/recorder", "type": "LIBRARY", "lib": "__idf_recorder", "reqs": [], "priv_reqs": ["board", "audio_sal", "audio_hal", "audio_stream", "esp_dispatcher", "esp_peripherals", "hal_service", "utils", "list"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/recorder/librecorder.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/recorder/recorder.c"], "include_dirs": ["include"]}, "res_binary": {"alias": "idf::res_binary", "target": "___idf_res_binary", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/res_binary", "type": "LIBRARY", "lib": "__idf_res_binary", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/res_binary/libres_binary.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/audio_alarmclock.mp3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/dingdong.mp3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/bat_chrg.mp3.S", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/nihaoxiaozhi.opus.S"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sdmmc_cmd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sdmmc_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sdmmc_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sdmmc_io.c", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sdmmc_mmc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sdmmc_sd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "sntp_service": {"alias": "idf::sntp_service", "target": "___idf_sntp_service", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/sntp_service", "type": "LIBRARY", "lib": "__idf_sntp_service", "reqs": [], "priv_reqs": ["utils", "hal_service", "hal_wifi", "drv_rtc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/sntp_service/libsntp_service.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/sntp_service/sntp_service.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/soc/libsoc.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/soc/lldesc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/dport_access_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/interrupts.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/gpio_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/uart_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/adc_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/gdma_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/spi_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/ledc_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/pcnt_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/rmt_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/sdm_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/i2s_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/i2c_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/timer_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/lcd_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/mcpwm_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/mpi_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/sdmmc_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/touch_sensor_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/twai_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/wdt_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/usb_dwc_periph.c", "E:/Espressif/533/v5.3.3/esp-idf/components/soc/esp32s3/rtc_io_periph.c"], "include_dirs": ["include", "esp32s3", "esp32s3/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/flash_brownout_hook.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/esp32s3/spi_flash_oct_flash_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_hpm_enable.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_drivers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_generic.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_issi.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_mxic.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_gd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_winbond.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_boya.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_mxic_opi.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_th.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/memspi_host_driver.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/cache_utils.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/flash_mmap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/flash_ops.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_wrap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/esp_flash_api.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/esp_flash_spi_init.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_os_func_app.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/spiffs/libspiffs.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/spiffs_api.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_cache.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_check.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_gc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_hydrogen.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_nucleus.c", "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport/transport.c", "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport/transport_ssl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport/transport_internal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport/transport_socks_proxy.c", "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "tone_partition": {"alias": "idf::tone_partition", "target": "___idf_tone_partition", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/tone_partition", "type": "LIBRARY", "lib": "__idf_tone_partition", "reqs": ["bootloader_support", "esp_app_format"], "priv_reqs": ["esp_actions", "esp_dispatcher", "audio_sal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/tone_partition/libtone_partition.a", "sources": ["E:/Espressif/esp-adf/components/tone_partition/tone_partition.c"], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/touch_element", "type": "LIBRARY", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/touch_element/libtouch_element.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/touch_element/touch_element.c", "E:/Espressif/533/v5.3.3/esp-idf/components/touch_element/touch_button.c", "E:/Espressif/533/v5.3.3/esp-idf/components/touch_element/touch_slider.c", "E:/Espressif/533/v5.3.3/esp-idf/components/touch_element/touch_matrix.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/unity/libunity.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity/src/unity.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity_compat.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity_runner.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity_utils_freertos.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity_utils_cache.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity_utils_memory.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/unity_port_esp32.c", "E:/Espressif/533/v5.3.3/esp-idf/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/usb", "type": "LIBRARY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/usb/libusb.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/usb/hcd_dwc.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/enum.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/hub.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/usb_helpers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/usb_host.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/usb_private.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/usbh.c", "E:/Espressif/533/v5.3.3/esp-idf/components/usb/usb_phy.c"], "include_dirs": ["include"]}, "utils": {"alias": "idf::utils", "target": "___idf_utils", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils", "type": "LIBRARY", "lib": "__idf_utils", "reqs": ["esp_dispatcher", "esp_peripherals", "app_update"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/utils/libutils.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils/math_util.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils/date_conver.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils/solar_terms.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils/str_util.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils/byte_convert.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils/system_util.c"], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/vfs/libvfs.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/vfs/vfs.c", "E:/Espressif/533/v5.3.3/esp-idf/components/vfs/vfs_eventfd.c", "E:/Espressif/533/v5.3.3/esp-idf/components/vfs/vfs_semihost.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/Partition.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/SPI_Flash.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/WL_Ext_Perf.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/WL_Ext_Safe.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/WL_Flash.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/crc32.cpp", "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/wifi_config.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/wifi_scan.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/wifi_ctrl.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/manager.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/handlers.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/scheme_console.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wifi_service": {"alias": "idf::wifi_service", "target": "___idf_wifi_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/wifi_service", "type": "LIBRARY", "lib": "__idf_wifi_service", "reqs": ["bt", "mbedtls", "nvs_flash", "esp_timer"], "priv_reqs": ["audio_sal", "esp_dispatcher", "esp_actions"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/wifi_service/libwifi_service.a", "sources": ["E:/Espressif/esp-adf/components/wifi_service/src/esp_wifi_setting.c", "E:/Espressif/esp-adf/components/wifi_service/src/wifi_service.c", "E:/Espressif/esp-adf/components/wifi_service/src/wifi_ssid_manager.c", "E:/Espressif/esp-adf/components/wifi_service/smart_config/smart_config.c", "E:/Espressif/esp-adf/components/wifi_service/blufi_config/blufi_config.c", "E:/Espressif/esp-adf/components/wifi_service/blufi_config/blufi_security.c", "E:/Espressif/esp-adf/components/wifi_service/airkiss_config/airkiss_config.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/port/os_xtensa.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/port/eloop.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/ap_config.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/ieee802_1x.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/wpa_auth.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/sta_info.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/ieee802_11.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/comeback_token.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/common/sae.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/common/dragonfly.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/common/wpa_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/bitfield.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-siv.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha256-kdf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/ccmp.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-gcm.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/crypto_ops.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/dh_group5.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/dh_groups.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/ms_funcs.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha256-prf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha1-prf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha384-prf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/md4-internal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha1-tprf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/common/ieee802_11_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/chap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/mschapv2.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/base64.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/ext_password.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/uuid.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/wpabuf.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/wpa_debug.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/json.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_attr_build.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_attr_parse.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_attr_process.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_dev_attr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_enrollee.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/common/sae_pk.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/rc4.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/des-internal.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-wrap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-unwrap.c", "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/xtensa/libxtensa.a", "sources": ["E:/Espressif/533/v5.3.3/esp-idf/components/xtensa/eri.c", "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa/xt_trax.c", "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa/xtensa_context.S", "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa/xtensa_intr_asm.S", "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa/xtensa_intr.c", "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "xzai": {"alias": "idf::xzai", "target": "___idf_xzai", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai", "type": "LIBRARY", "lib": "__idf_xzai", "reqs": [], "priv_reqs": ["board", "audio_sal", "audio_hal", "audio_stream", "audio_recorder", "esp_dispatcher", "esp_peripherals", "78__esp-opus", "app_update", "mqtt", "hal_service", "recorder", "player", "utils", "res_binary", "hal_wifi", "drv_beep", "drv_battery", "drv_lcd_i80", "drv_bt", "drv_key", "list", "espressif__esp_websocket_client", "esp-ml307", "idf_http_rest_client", "gzip_miniz"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/esp-idf/xzai/libxzai.a", "sources": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/audio_encoder.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/audio_player.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/voice.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/xzai.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/xzai_board_info.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/xzai_check_ver.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/xzai_task.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/xzai_things.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/protocols/mqtt_protocol.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/protocols/protocol.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/protocols/udp_client.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/protocols/websocket_client.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/protocols/websocket_protocol.c", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/thing.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/thing_manager.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/things/battery.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/things/blaklight.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/things/bluetooth.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/things/lamp.cc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai/iot/things/speaker.cc"], "include_dirs": ["include", "protocols", "iot", "iot/things"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/Espressif/533/v5.3.3/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/Espressif/533/v5.3.3/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "bootloader_support", "spi_flash", "app_update", "partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3", "esp32s3"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/Espressif/533/v5.3.3/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "../spi_flash/include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3", "esp32s3/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/touch_element", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "E:/Espressif/533/v5.3.3/esp-idf/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main", "lib": "__idf_main", "reqs": [], "priv_reqs": ["78__esp-opus", "espressif__esp_websocket_client"], "managed_reqs": [], "managed_priv_reqs": ["78__esp-opus", "espressif__esp_websocket_client"], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/include", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/gui_port", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/custom", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/generated", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/images", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/misc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/guider/user", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/misc", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/config", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/www"]}, "adf_utils": {"alias": "idf::adf_utils", "target": "___idf_adf_utils", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/adf_utils", "lib": "__idf_adf_utils", "reqs": [], "priv_reqs": ["esp_http_client", "espressif__jsmn", "mbedtls", "audio_sal"], "managed_reqs": [], "managed_priv_reqs": ["espressif__jsmn"], "include_dirs": ["cloud_services/include", "include"]}, "audio_board": {"alias": "idf::audio_board", "target": "___idf_audio_board", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_board", "lib": "__idf_audio_board", "reqs": [], "priv_reqs": ["esp_peripherals", "audio_sal", "audio_hal", "esp_dispatcher", "display_service", "espressif__esp_lcd_ili9341"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_lcd_ili9341"], "include_dirs": ["./include"]}, "audio_hal": {"alias": "idf::audio_hal", "target": "___idf_audio_hal", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_hal", "lib": "__idf_audio_hal", "reqs": [], "priv_reqs": ["audio_sal", "audio_board", "mbedtls", "esp_peripherals", "display_service", "esp_dispatcher"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include", "./driver/es8388", "./driver/es8374", "./driver/es8311", "./driver/es8156", "./driver/es7243", "./driver/es7148", "./driver/es7210", "./driver/es7243e", "./driver/tas5805m", "./driver/include", "./driver/zl38063", "./driver/zl38063/api_lib", "./driver/zl38063/example_apps", "./driver/zl38063/firmware"]}, "audio_mixer": {"alias": "idf::audio_mixer", "target": "___idf_audio_mixer", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_mixer", "lib": "__idf_audio_mixer", "reqs": ["audio_sal", "esp-adf-libs"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "audio_pipeline": {"alias": "idf::audio_pipeline", "target": "___idf_audio_pipeline", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_pipeline", "lib": "__idf_audio_pipeline", "reqs": ["audio_sal", "esp-adf-libs"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "audio_recorder": {"alias": "idf::audio_recorder", "target": "___idf_audio_recorder", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_recorder", "lib": "__idf_audio_recorder", "reqs": ["audio_sal", "audio_pipeline", "esp-sr", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "audio_sal": {"alias": "idf::audio_sal", "target": "___idf_audio_sal", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_sal", "lib": "__idf_audio_sal", "reqs": ["efuse"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "audio_stream": {"alias": "idf::audio_stream", "target": "___idf_audio_stream", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/audio_stream", "lib": "__idf_audio_stream", "reqs": ["audio_pipeline", "driver", "audio_sal", "esp_http_client", "tcp_transport", "spiffs", "audio_board", "esp-adf-libs", "bootloader_support", "esp_dispatcher", "esp_actions", "tone_partition", "mbedtls", "esp-sr"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "battery_service": {"alias": "idf::battery_service", "target": "___idf_battery_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/battery_service", "lib": "__idf_battery_service", "reqs": ["audio_sal", "esp_peripherals", "esp_timer"], "priv_reqs": ["esp_adc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "./monitors/include"]}, "bluetooth_service": {"alias": "idf::bluetooth_service", "target": "___idf_bluetooth_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/bluetooth_service", "lib": "__idf_bluetooth_service", "reqs": ["bt", "audio_sal", "audio_pipeline", "esp_peripherals", "audio_stream", "audio_hal"], "priv_reqs": ["nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "clouds": {"alias": "idf::clouds", "target": "___idf_clouds", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/clouds", "lib": "__idf_clouds", "reqs": [], "priv_reqs": ["espressif__zlib"], "managed_reqs": [], "managed_priv_reqs": ["espressif__zlib"], "include_dirs": ["./dueros/lightduer/include", "./volc_engine_rtc/include"]}, "coredump_upload_service": {"alias": "idf::coredump_upload_service", "target": "___idf_coredump_upload_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/coredump_upload_service", "lib": "__idf_coredump_upload_service", "reqs": ["esp_http_client", "espcoredump", "spi_flash", "esp_rom"], "priv_reqs": ["audio_sal", "esp_dispatcher"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "display_service": {"alias": "idf::display_service", "target": "___idf_display_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/display_service", "lib": "__idf_display_service", "reqs": [], "priv_reqs": ["audio_sal", "audio_board", "audio_hal", "esp_peripherals"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "led_indicator/include", "led_bar/include"]}, "dueros_service": {"alias": "idf::dueros_service", "target": "___idf_dueros_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/dueros_service", "lib": "__idf_dueros_service", "reqs": [], "priv_reqs": ["clouds", "mbedtls", "audio_board", "audio_hal", "esp-adf-libs", "audio_sal", "esp_peripherals", "audio_recorder", "wifi_service"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp-adf-libs": {"alias": "idf::esp-adf-libs", "target": "___idf_esp-adf-libs", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp-adf-libs", "lib": "__idf_esp-adf-libs", "reqs": ["audio_pipeline", "audio_sal", "espressif__nghttp", "esp-tls", "esp_netif"], "priv_reqs": ["espressif__nghttp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__nghttp"], "include_dirs": ["./esp_h264/include", "./esp_audio/include", "./esp_codec/include/codec", "./esp_codec/include/processing", "./esp_new_jpeg/include", "./media_lib_sal/include", "./media_lib_sal/include/port", "./esp_muxer/include", "./esp_media_protocols/include"]}, "esp-sr": {"alias": "idf::esp-sr", "target": "___idf_esp-sr", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp-sr", "lib": "__idf_esp-sr", "reqs": ["json", "spiffs", "esp_partition"], "priv_reqs": ["spi_flash", "espressif__dl_fft", "espressif__esp-dsp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__dl_fft", "espressif__esp-dsp"], "include_dirs": ["esp-tts/esp_tts_chinese/include", "include/esp32s3", "src/include"]}, "esp_actions": {"alias": "idf::esp_actions", "target": "___idf_esp_actions", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_actions", "lib": "__idf_esp_actions", "reqs": ["audio_sal", "nvs_flash", "spi_flash"], "priv_reqs": ["esp_dispatcher", "wifi_service", "display_service", "esp-adf-libs", "audio_pipeline", "audio_sal", "dueros_service", "audio_recorder"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_codec_dev": {"alias": "idf::esp_codec_dev", "target": "___idf_esp_codec_dev", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_codec_dev", "lib": "__idf_esp_codec_dev", "reqs": ["driver"], "priv_reqs": ["freertos"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "device/include"]}, "esp_coze": {"alias": "idf::esp_coze", "target": "___idf_esp_coze", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_coze", "lib": "__idf_esp_coze", "reqs": [], "priv_reqs": ["esp_http_client", "espressif__esp_websocket_client"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_websocket_client"], "include_dirs": ["./include"]}, "esp_dispatcher": {"alias": "idf::esp_dispatcher", "target": "___idf_esp_dispatcher", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_dispatcher", "lib": "__idf_esp_dispatcher", "reqs": [], "priv_reqs": ["audio_sal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event_cast": {"alias": "idf::esp_event_cast", "target": "___idf_esp_event_cast", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_event_cast", "lib": "__idf_esp_event_cast", "reqs": ["audio_sal"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_peripherals": {"alias": "idf::esp_peripherals", "target": "___idf_esp_peripherals", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/esp_peripherals", "lib": "__idf_esp_peripherals", "reqs": ["driver", "audio_hal", "audio_sal", "fatfs", "console", "audio_pipeline", "audio_board", "spiffs", "display_service", "esp_dispatcher", "bt", "mbedtls", "wpa_supplicant", "nvs_flash", "esp_lcd", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include", "./lib/adc_button", "./lib/gpio_isr", "./lib/button", "./lib/blufi", "./lib/IS31FL3216", "./lib/aw2013", "./lib/tca9554", "./driver/i2c_bus", "./lib/sdcard", "./lib/touch"]}, "input_key_service": {"alias": "idf::input_key_service", "target": "___idf_input_key_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/input_key_service", "lib": "__idf_input_key_service", "reqs": [], "priv_reqs": ["audio_sal", "esp_peripherals"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "ota_service": {"alias": "idf::ota_service", "target": "___idf_ota_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/ota_service", "lib": "__idf_ota_service", "reqs": ["app_update", "esp_https_ota", "esp_app_format"], "priv_reqs": ["esp_peripherals", "audio_pipeline", "audio_sal", "audio_stream"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "playlist": {"alias": "idf::playlist", "target": "___idf_playlist", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/playlist", "lib": "__idf_playlist", "reqs": ["audio_sal", "nvs_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "tone_partition": {"alias": "idf::tone_partition", "target": "___idf_tone_partition", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/tone_partition", "lib": "__idf_tone_partition", "reqs": ["bootloader_support", "esp_app_format"], "priv_reqs": ["esp_actions", "esp_dispatcher", "audio_sal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_service": {"alias": "idf::wifi_service", "target": "___idf_wifi_service", "prefix": "idf", "dir": "E:/Espressif/esp-adf/components/wifi_service", "lib": "__idf_wifi_service", "reqs": ["bt", "mbedtls", "nvs_flash", "esp_timer"], "priv_reqs": ["audio_sal", "esp_dispatcher", "esp_actions"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "board": {"alias": "idf::board", "target": "___idf_board", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/board", "lib": "__idf_board", "reqs": [], "priv_reqs": ["audio_sal", "audio_hal", "esp_dispatcher", "esp_peripherals", "display_service"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./board_ddclock", "./codec_driver"]}, "drv_adc": {"alias": "idf::drv_adc", "target": "___idf_drv_adc", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_adc", "lib": "__idf_drv_adc", "reqs": [], "priv_reqs": ["esp_adc", "hal_service", "board"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_battery": {"alias": "idf::drv_battery", "target": "___idf_drv_battery", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_battery", "lib": "__idf_drv_battery", "reqs": ["esp_adc", "board", "audio_sal", "battery_service", "utils", "drv_pca9557"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_beep": {"alias": "idf::drv_beep", "target": "___idf_drv_beep", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_beep", "lib": "__idf_drv_beep", "reqs": [], "priv_reqs": ["hal_service", "board", "drv_pca9557"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_bt": {"alias": "idf::drv_bt", "target": "___idf_drv_bt", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_bt", "lib": "__idf_drv_bt", "reqs": ["esp_driver_gpio", "board", "drv_pca9557"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_encoder": {"alias": "idf::drv_encoder", "target": "___idf_drv_encoder", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_encoder", "lib": "__idf_drv_encoder", "reqs": ["esp_driver_gpio", "esp_driver_pcnt", "board"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_htu21d": {"alias": "idf::drv_htu21d", "target": "___idf_drv_htu21d", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_htu21d", "lib": "__idf_drv_htu21d", "reqs": ["esp_peripherals"], "priv_reqs": ["hal_i2c", "hal_service"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_key": {"alias": "idf::drv_key", "target": "___idf_drv_key", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_key", "lib": "__idf_drv_key", "reqs": [], "priv_reqs": ["esp_driver_gpio", "board", "drv_pca9557"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_lcd_i80": {"alias": "idf::drv_lcd_i80", "target": "___idf_drv_lcd_i80", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_lcd_i80", "lib": "__idf_drv_lcd_i80", "reqs": ["esp_lcd", "esp_driver_gpio", "board"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_pca9557": {"alias": "idf::drv_pca9557", "target": "___idf_drv_pca9557", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_pca9557", "lib": "__idf_drv_pca9557", "reqs": [], "priv_reqs": ["hal_i2c", "board"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "drv_rtc": {"alias": "idf::drv_rtc", "target": "___idf_drv_rtc", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/drv_rtc", "lib": "__idf_drv_rtc", "reqs": ["hal_i2c", "hal_service"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp-ml307": {"alias": "idf::esp-ml307", "target": "___idf_esp-ml307", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp-ml307", "lib": "__idf_esp-ml307", "reqs": ["esp_driver_gpio", "esp_driver_uart", "esp-tls", "esp_http_client", "mqtt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mpg123": {"alias": "idf::esp_mpg123", "target": "___idf_esp_mpg123", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_mpg123", "lib": "__idf_esp_mpg123", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_mpg123/mpg123/src/include"]}, "esp_ogg": {"alias": "idf::esp_ogg", "target": "___idf_esp_ogg", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_ogg", "lib": "__idf_esp_ogg", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["ogg"]}, "esp_rlottie": {"alias": "idf::esp_rlottie", "target": "___idf_esp_rlottie", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_rlottie", "lib": "__idf_esp_rlottie", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_rlottie/rlottie/inc"]}, "esp_simple_dsp": {"alias": "idf::esp_simple_dsp", "target": "___idf_esp_simple_dsp", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_simple_dsp", "lib": "__idf_esp_simple_dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "esp_vorbis": {"alias": "idf::esp_vorbis", "target": "___idf_esp_vorbis", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis", "lib": "__idf_esp_vorbis", "reqs": ["esp_ogg"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis/vorbis/include", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_vorbis/vorbis/lib"]}, "gzip_miniz": {"alias": "idf::gzip_miniz", "target": "___idf_gzip_miniz", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/gzip_miniz", "lib": "__idf_gzip_miniz", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": [".", "include"]}, "hal_i2c": {"alias": "idf::hal_i2c", "target": "___idf_hal_i2c", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_i2c", "lib": "__idf_hal_i2c", "reqs": ["board", "audio_sal", "esp_peripherals", "esp_driver_gpio"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "hal_service": {"alias": "idf::hal_service", "target": "___idf_hal_service", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_service", "lib": "__idf_hal_service", "reqs": ["board", "audio_sal", "esp_peripherals"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "hal_wifi": {"alias": "idf::hal_wifi", "target": "___idf_hal_wifi", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/hal_wifi", "lib": "__idf_hal_wifi", "reqs": ["hal_service", "json", "esp_http_server", "esp_http_client"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "idf_http_rest_client": {"alias": "idf::idf_http_rest_client", "target": "___idf_idf_http_rest_client", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/idf_http_rest_client", "lib": "__idf_idf_http_rest_client", "reqs": ["esp_http_client", "esp-tls", "json"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "list": {"alias": "idf::list", "target": "___idf_list", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/list", "lib": "__idf_list", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "lvgl": {"alias": "idf::lvgl", "target": "___idf_lvgl", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl", "lib": "__idf_lvgl", "reqs": ["esp_timer", "esp_rlottie", "fatfs"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/src", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/../", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/examples", "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/lvgl/demos"]}, "player": {"alias": "idf::player", "target": "___idf_player", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/player", "lib": "__idf_player", "reqs": [], "priv_reqs": ["board", "audio_sal", "audio_hal", "audio_stream", "esp_dispatcher", "esp_peripherals", "display_service", "hal_service", "esp_vorbis", "esp_mpg123", "list", "utils"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "qweather": {"alias": "idf::qweather", "target": "___idf_qweather", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/qweather", "lib": "__idf_qweather", "reqs": [], "priv_reqs": ["hal_service", "hal_wifi", "idf_http_rest_client", "gzip_miniz"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "recorder": {"alias": "idf::recorder", "target": "___idf_recorder", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/recorder", "lib": "__idf_recorder", "reqs": [], "priv_reqs": ["board", "audio_sal", "audio_hal", "audio_stream", "esp_dispatcher", "esp_peripherals", "hal_service", "utils", "list"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "res_binary": {"alias": "idf::res_binary", "target": "___idf_res_binary", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/res_binary", "lib": "__idf_res_binary", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sntp_service": {"alias": "idf::sntp_service", "target": "___idf_sntp_service", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/sntp_service", "lib": "__idf_sntp_service", "reqs": [], "priv_reqs": ["utils", "hal_service", "hal_wifi", "drv_rtc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "utils": {"alias": "idf::utils", "target": "___idf_utils", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/utils", "lib": "__idf_utils", "reqs": ["esp_dispatcher", "esp_peripherals", "app_update"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "xzai": {"alias": "idf::xzai", "target": "___idf_xzai", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/xzai", "lib": "__idf_xzai", "reqs": [], "priv_reqs": ["board", "audio_sal", "audio_hal", "audio_stream", "audio_recorder", "esp_dispatcher", "esp_peripherals", "78__esp-opus", "app_update", "mqtt", "hal_service", "recorder", "player", "utils", "res_binary", "hal_wifi", "drv_beep", "drv_battery", "drv_lcd_i80", "drv_bt", "drv_key", "list", "espressif__esp_websocket_client", "esp-ml307", "idf_http_rest_client", "gzip_miniz"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "protocols", "iot", "iot/things"]}, "78__esp-opus": {"alias": "idf::78__esp-opus", "target": "___idf_78__esp-opus", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/78__esp-opus", "lib": "__idf_78__esp-opus", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__cmake_utilities": {"alias": "idf::espressif__cmake_utilities", "target": "___idf_espressif__cmake_utilities", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__cmake_utilities", "lib": "__idf_espressif__cmake_utilities", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "espressif__dl_fft": {"alias": "idf::espressif__dl_fft", "target": "___idf_espressif__dl_fft", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__dl_fft", "lib": "__idf_espressif__dl_fft", "reqs": ["espressif__esp-dsp"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": [".", "base", "base/isa"]}, "espressif__esp-dsp": {"alias": "idf::espressif__esp-dsp", "target": "___idf_espressif__esp-dsp", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp-dsp", "lib": "__idf_espressif__esp-dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["modules/dotprod/include", "modules/support/include", "modules/support/mem/include", "modules/windows/include", "modules/windows/hann/include", "modules/windows/blackman/include", "modules/windows/blackman_harris/include", "modules/windows/blackman_nuttall/include", "modules/windows/nuttall/include", "modules/windows/flat_top/include", "modules/iir/include", "modules/fir/include", "modules/math/include", "modules/math/add/include", "modules/math/sub/include", "modules/math/mul/include", "modules/math/addc/include", "modules/math/mulc/include", "modules/math/sqrt/include", "modules/matrix/mul/include", "modules/matrix/add/include", "modules/matrix/addc/include", "modules/matrix/mulc/include", "modules/matrix/sub/include", "modules/matrix/include", "modules/fft/include", "modules/dct/include", "modules/conv/include", "modules/common/include", "modules/matrix/mul/test/include", "modules/kalman/ekf/include", "modules/kalman/ekf_imu13states/include"]}, "espressif__esp_lcd_ili9341": {"alias": "idf::espressif__esp_lcd_ili9341", "target": "___idf_espressif__esp_lcd_ili9341", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_lcd_ili9341", "lib": "__idf_espressif__esp_lcd_ili9341", "reqs": ["driver", "esp_lcd"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_websocket_client": {"alias": "idf::espressif__esp_websocket_client", "target": "___idf_espressif__esp_websocket_client", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__esp_websocket_client", "lib": "__idf_espressif__esp_websocket_client", "reqs": ["lwip", "esp-tls", "tcp_transport", "http_parser", "esp_event"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__jsmn": {"alias": "idf::espressif__jsmn", "target": "___idf_espressif__jsmn", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__jsmn", "lib": "__idf_espressif__jsmn", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__nghttp": {"alias": "idf::espressif__nghttp", "target": "___idf_espressif__nghttp", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__nghttp", "lib": "__idf_espressif__nghttp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "nghttp2/lib/includes"]}, "espressif__zlib": {"alias": "idf::espressif__zlib", "target": "___idf_espressif__zlib", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__zlib", "lib": "__idf_espressif__zlib", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["zlib"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/gdbinit/symbols", "02_prefix_map": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/gdbinit/prefix_map", "03_py_extensions": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/gdbinit/py_extensions", "04_connect": "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32s3-builtin.cfg"}
#include "rtc_pcf8563.h"

static uint8_t decimal2bcd (uint8_t decimal)
{
	return (((decimal / 10) << 4) | (decimal % 10));
}

static uint8_t bcd2decimal(uint8_t bcd)
{
	return (((bcd >> 4) * 10) + (bcd & 0x0f));
}

pcf8563_err_t rtc_pcf8563_init(i2c_bus_handle_t i2c)
{
	uint8_t clear = 0x00;
	uint8_t reg[2] = {
		PCF8563_CONTROL_STATUS1,
		PCF8563_CONTROL_STATUS2
	};

    i2c_bus_write_bytes(i2c, PCF8563_ADDRESS, &reg[0], 1, &clear, 1);
    i2c_bus_write_bytes(i2c, PCF8563_ADDRESS, &reg[1], 1, &clear, 1);

    return PCF8563_OK;
}

pcf8563_err_t rtc_pcf8563_read(i2c_bus_handle_t i2c, rtc_time_t *time)
{
	uint8_t bcd;
	uint8_t t_data[PCF8563_TIME_SIZE] = {0};
	uint16_t century;

	uint8_t reg = PCF8563_SECONDS;

    i2c_bus_read_bytes(i2c, PCF8563_ADDRESS, &reg, 1, t_data, PCF8563_TIME_SIZE);
	
	/* 0..59 */
	bcd = t_data[0] & B(01111111);
	time->tm_sec = bcd2decimal(bcd);

	/* 0..59 */
	bcd = t_data[1] & B(01111111);
	time->tm_min = bcd2decimal(bcd);

	/* 0..23 */
	bcd = t_data[2] & B(00111111);
	time->tm_hour = bcd2decimal(bcd);

	/* 1..31 */
	bcd = t_data[3] & B(00111111);
	time->tm_mday = bcd2decimal(bcd);

	/* 0..6 */
	bcd = t_data[4] & B(00000111);
	time->tm_wday = bcd2decimal(bcd);

	/* 1..12 */
	bcd = t_data[5] & B(00011111);
	time->tm_mon = bcd2decimal(bcd);

	/* If century bit set assume it is 2000. */
	century = (t_data[5] & PCF8563_CENTURY_BIT) ? 2000 : 1900;

	/* Number of years since 1900. */
	bcd = t_data[6] & B(11111111);
	time->tm_year = bcd2decimal(bcd) + century;
	
	/* low voltage warning */
	if (t_data[0] & B(10000000)) {
			return PCF8563_ERR_LOW_VOLTAGE;
	}

    return PCF8563_OK;
}

pcf8563_err_t rtc_pcf8563_write(i2c_bus_handle_t i2c, rtc_time_t *time)
{
	uint8_t bcd;
	uint8_t t_data[PCF8563_TIME_SIZE] = {0};

	uint8_t reg = PCF8563_SECONDS;
	
	/* reg */
//	t_data[0] = PCF8563_SECONDS;
	
	/* 0..59 */
	bcd = decimal2bcd(time->tm_sec);
	t_data[0] = bcd & B(01111111);

	/* 0..59 */
	bcd = decimal2bcd(time->tm_min);
	t_data[1] = bcd & B(01111111);

	/* 0..23 */
	bcd = decimal2bcd(time->tm_hour);
	t_data[2] = bcd & B(00111111);

	/* 1..31 */
	bcd = decimal2bcd(time->tm_mday);
	t_data[3] = bcd & B(00111111);

	/* 0..6 */
	get_day_of_week(time->tm_year, time->tm_mon, time->tm_mday, &time->tm_wday);
	bcd = decimal2bcd(time->tm_wday);
	t_data[4] = bcd & B(00000111);

	/* 1..12 */
	bcd = decimal2bcd(time->tm_mon);
	t_data[5] = bcd & B(00011111);

	/* If 2000 set the century bit. */
	if (time->tm_year >= 2000) {
		t_data[5] |= PCF8563_CENTURY_BIT;
	}

	/* 0..99 */
	bcd = decimal2bcd(time->tm_year % 100);
	t_data[6] = bcd & B(11111111);

    i2c_bus_write_bytes(i2c, PCF8563_ADDRESS, &reg, 1, t_data, PCF8563_TIME_SIZE);
	
    return PCF8563_OK;
}




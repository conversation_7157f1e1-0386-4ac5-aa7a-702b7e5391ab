#ifndef _RTC_PCF8563_H
#define _RTC_PCF8563_H

#include "hal_i2c.h"
#include "rtc_typedef.h"
#include "rtc_util.h"

#define	PCF8563_ADDRESS	         (0x51 << 1)

#define	PCF8563_CONTROL_STATUS1  (0x00)
#define	PCF8563_TESTC            B(00001000)
#define	PCF8563_STOP             B(00100000)
#define	PCF8563_TEST1            B(10000000)
#define	PCF8563_CONTROL_STATUS2  (0x01)
#define	PCF8563_TIE              B(00000001)
#define	PCF8563_AIE              B(00000010)
#define	PCF8563_TF               B(00000100)
#define	PCF8563_AF               B(00001000)
#define	PCF8563_TI_TP            B(00010000)
#define	PCF8563_SECONDS          (0x02)
#define	PCF8563_MINUTES          (0x03)
#define	PCF8563_HOURS            (0x04)
#define	PCF8563_DAY              (0x05)
#define	PCF8563_WEEKDAY          (0x06)
#define	PCF8563_MONTH            (0x07)
#define	PCF8563_YEAR             (0x08)
#define	PCF8563_TIME_SIZE        (0x07)
#define	PCF8563_CENTURY_BIT      B(10000000)

#define PCF8563_MINUTE_ALARM     (0x09)
#define PCF8563_HOUR_ALARM       (0x0a)
#define PCF8563_DAY_ALARM        (0x0b)
#define PCF8563_WEEKDAY_ALARM    (0x0c)
#define PCF8563_ALARM_DISABLE    B(10000000)
#define PCF8563_ALARM_NONE       (0xff)
#define PCF8563_ALARM_SIZE       (0x04)

#define PCF8563_TIMER_CONTROL    (0x0e)
#define PCF8563_TIMER_ENABLE     B(10000000)
#define PCF8563_TIMER_4_096KHZ   B(00000000)
#define PCF8563_TIMER_64HZ       B(00000001)
#define PCF8563_TIMER_1HZ        B(00000010)
#define PCF8563_TIMER_1_60HZ     B(00000011)
#define PCF8563_TIMER            (0x0f)

/* IOCTL commands */
#define PCF8563_ALARM_SET        (0x0900)
#define PCF8563_ALARM_READ       (0x0901)
#define PCF8563_CONTROL_STATUS1_READ     (0x0000)
#define PCF8563_CONTROL_STATUS1_WRITE    (0x0001)
#define PCF8563_CONTROL_STATUS2_READ     (0x0100)
#define PCF8563_CONTROL_STATUS2_WRITE    (0x0101)
#define PCF8563_TIMER_CONTROL_READ       (0x0e00)
#define PCF8563_TIMER_CONTROL_WRITE      (0x0e01)
#define PCF8563_TIMER_READ               (0x0f00)
#define PCF8563_TIMER_WRITE              (0x0f01)

#define PCF8563_ERROR_NOTTY      (-1)
#define PCF8563_OK               (0x00)
#define PCF8563_ERR_LOW_VOLTAGE  (0x80)

typedef uint8_t pcf8563_err_t;

pcf8563_err_t rtc_pcf8563_init(i2c_bus_handle_t i2c);
pcf8563_err_t rtc_pcf8563_read(i2c_bus_handle_t i2c, rtc_time_t *time);
pcf8563_err_t rtc_pcf8563_write(i2c_bus_handle_t i2c, rtc_time_t *time);

#endif



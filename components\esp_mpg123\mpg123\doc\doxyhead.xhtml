<!--#include virtual="/header.html" -->
<title>$title</title>
<link href="/doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<!--#include virtual="/top.shtml" -->
<h1>API documentation for libmpg123, libout123, and libsyn123</h1>
<div style="padding:1em;">
	<strong>Note:</strong>
	This API doc is automatically generated from the current development version that you can get via Subversion or as a daily snapshot from <a href="http://mpg123.org/snapshot">http://mpg123.org/snapshot</a>.
	There may be differences (additions) compared to the latest stable release. See
	<a href="https://mpg123.org/trunk/NEWS.libmpg123">NEWS.libmpg123</a>,
	<a href="https://mpg123.org/trunk/NEWS.libout123">NEWS.libout123</a>,
	<a href="https://mpg123.org/trunk/NEWS.libsyn123">NEWS.libsyn123</a>,
	and the overall <a href="https://mpg123.org/trunk/NEWS">NEWS</a> file on libmpg123 versions and important changes between them.<br />
	Let me emphasize that the policy for the lib*123 family is to always stay backwards compatible -- only <em>additions</em> are planned (and it's not yet planned to change the plans;-).
</div>
<div> <!-- for some reason, doxygen closes a div where non is open, bug in my version? -->

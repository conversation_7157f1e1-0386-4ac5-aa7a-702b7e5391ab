<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=windows-1252"/>
	<title></title>
	<meta name="generator" content="LibreOffice 7.0.3.1 (Windows)"/>
	<meta name="created" content="2021-02-03T20:00:54"/>
	<meta name="changed" content="2021-02-13T16:28:26.399000000"/>
	<style type="text/css">
		@page { size: 8.5in 11in; margin: 0.79in }
		p { margin-bottom: 0.1in; direction: ltr; color: #00000a; line-height: 115%; text-align: left; orphans: 2; widows: 2; background: transparent }
		p.western { font-family: "Arial", sans-serif; font-size: 12pt; so-language: en-US }
		p.cjk { font-family: "NSimSun"; font-size: 12pt; so-language: zh-CN }
		p.ctl { font-family: "Arial"; font-size: 12pt; so-language: hi-IN }
		a:link { color: #000080; so-language: zxx; text-decoration: underline }
		a:visited { color: #800000; so-language: zxx; text-decoration: underline }
	</style>
</head>
<body lang="en-US" text="#00000a" link="#000080" vlink="#800000" dir="ltr"><p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<b>GENERAL INFORMATION ON RUNNING MPG123 IN A WINDOWS ENVIR</b><b>O</b><b>NMENT</b></p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<i>(Caution- This is a brief guide, based on my experience running
MPG123 under Windows.  This is NOT a sanctioned document from the
MPG123 team.  It has not been reviewed by MPG123 for accuracy or
completeness.  Use this information at your own risk. DON'T submit
tickets to MPG123 based on anything you read in this document, and
there is NO guarantee as to the validity of information contained
herein. -- J. Amick - 2021-02-13 ) </i>
</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
A portable (no installation required) MPG123.EXE can be downloaded
from &ldquo;<span style="font-variant: normal"><font color="#330000"><font face="sans, sans-serif"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="font-weight: normal">Win32
and Win64 binaries&rdquo; at </span></span></span></font></font></span>WWW.MPG123.DE<span style="font-variant: normal"><font color="#330000"><font face="sans, sans-serif"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="font-weight: normal">.
 </span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font face="Arial, sans-serif"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="font-weight: normal">The
file you want is </span></span></span></font></font></span><font color="#000080"><span lang="zxx"><span style="text-decoration: none">mpg123-v-static-x86-bb.zip</span></span></font><font color="#000080"><span lang="zxx"><span style="font-variant: normal"><font color="#330000"><font face="Arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">,</span></span></span></span></font></font></font></span></span></font><span style="font-variant: normal"><font color="#330000"><font face="Arial, sans-serif"><font size="4" style="font-size: 14pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">
</span></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#330000"><font face="Arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">where
v (version) is currently &ldquo;1.26.4&rdquo; </span></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">and
bb (your system architecture) is either &ldquo;32&rdquo; or &ldquo;64&rdquo;
bit.. Extract the entire downloaded zip.  Consider creating a new
target directory named MPG123 in &ldquo;Program Files&rdquo;.  Make
sure the target directory is included in your PATH system environment
variable- add if necessary. </span></span></span></span></font></font></span>
</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">Command
Line usage: </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><b>mpg123</b></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">
</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">[
</span></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><b>options
</b></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">]
</span></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><i><span style="text-decoration: none"><span style="font-weight: normal">file</span></span></i></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">
... </span></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><i><span style="text-decoration: none"><span style="font-weight: normal">|
URL </span></span></i></span></font></font></font></span><span style="font-variant: normal"><font color="#444444"><font face="verdana, helvetica, arial, sans-serif"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">...
| -</span></span></span></span></font></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">
</span></span></span></span></font></font></span>
</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">file
&hellip;</span></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">Windows
full path file name(s), </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><i><span style="text-decoration: none"><span style="font-weight: normal">surrounded
by double quotes</span></span></i></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">.
 The * wildcard is OK.  For example:  &ldquo;F:\Music\*.mp3&rdquo;. 
</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">Surrounding
d</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">ouble
quotes allow file/folder names contain</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">ing</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">
blanks.</span></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">Option
[-a </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><i><span style="text-decoration: none"><span style="font-weight: normal">dev</span></span></i></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">]
</span></span></span></span></font></font></span>
</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">Specify
the audio device to use.  If not present on the command line, MPG123
will use the Windows default audio device- sharing the same audio
channel with normal Windows sounds.  If you want MPG123 to play
through a separate/dedicated audio channel, you have to provide
another audio device (sound card, USB sound device, etc).  To see
what sound devices are </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">installed
and </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">available
in your Windows system, access and run the &ldquo;Device Manager&rdquo;
(devmgmt.msc).  Click to expand &ldquo;Sound, video and game
controllers&rdquo;.  Identify the desired sound device and
double-click or right click to &ldquo;Properties&rdquo;.  Select the
&ldquo;Details&rdquo; tab, and look for Property &ldquo;Device
instance path&rdquo; (Win 10) or &ldquo;Device instance id&rdquo;
(Win XP).  The &ldquo;Value&rdquo; box will contain one or more
lines- the first line is the one you want- and looks something like
this: USB\VID_0D8C&amp;PID_0014&amp;MI_00\6&amp;175BA917&amp;0&amp;0000.
 If you type this line on the command line, surround it with double
quotes.  Consider saving this </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">(including
the surrounding double quotes) </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">as
a System environment variable, </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">to</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">
eliminate a lot of future typing if debugging a BAT file: System
Properties &gt; Environment Variables &gt; System Variables.</span></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">If
you have multiple sound devices in your computer, confirm the Windows
Default Sound Device is set to the one you want for </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">any
audio </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">other
than </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">that
</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">generated
by </span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">MPG123.</span></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><b>CASE
STUDY- SOUND SOURCE FOR TELEPHONE SYSTEM MUSIC ON HOLD</b></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">I
needed a music source to implement &ldquo;Music on Hold&rdquo; in our
office phone system.  In the past, personal CD/MP3 players have
sufficed, but none would last more than a year playing continuously. 
All the inexpensive I-Pod type MP3 players I found couldn&rsquo;t
play and charge at the same time.  The solution came in the form of
MPG123 playing a folder of MP3 files on an old XP computer running
unattended in the phone closet, still doing batch file document
backups.  A channel of MOH audio exclusively for the phone system was
added in the form of a Sabrent USB-SBCV plug-in audio generator
device capable of continuous</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><i><span style="text-decoration: none"><span style="font-weight: normal">
</span></span></i></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">play
for less than $10 (Amazon).  This device requires NO external
drivers- truly plug and play.</span></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; font-variant: normal; letter-spacing: normal; font-style: normal; font-weight: normal; line-height: 100%; text-decoration: none">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">This
application required MPG123 to play a continuous loop of every MP3
file found in a folder named &ldquo;Music On Hold&rdquo;.  MPG123 has
the -Z option which does this directly, but files are played in
random order.  I preferred to have the files looped in alphabetical
order, which MPG123 will not directly do.  A short batch file
(created in the same directory as MPG123.</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">EXE</span></span></span></span></font></font></span><span style="font-variant: normal"><font color="#330000"><font size="3" style="font-size: 12pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="text-decoration: none"><span style="font-weight: normal">)
takes care of this problem.  As a template, copy and paste the
following lines into Notepad, and save as a BAT file:</span></span></span></span></font></font></span></p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
@echo off</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
rem    Start up an infinite loop</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
for /L %%n in () do (</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
rem    Play mp3 files in alphabetical order from specified directory</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
for %%f in (&quot;G:\music on hold\*.mp3&quot;) do (</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
    mpg123 -m -a %Sabrent% ^&quot;%%f^&quot;  ))</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
This batch file presumes a Sabrent system environment variable set
from the sound device property details as described earlier. 
Substitute the correct location of your MP3 files.  Remove the -m
option if you want stereo.  
</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
<br/>

</p>
<p class="western" align="left" style="margin-bottom: 0in; line-height: 100%">
This batch file can be initiated directly from a scheduled task
created to launch when windows starts (or other trigger).  Each
version of Windows has its own idiosyncrasies starting a batch file
from a scheduled task, and I recommend a Google search if you have
difficulties.  Ditto for having the batch file run in a minimized CMD
window, or no window at all.  Consider flushing all console output
(stdout and stderr) when you invoke the batch file from a scheduled
task.  For this batch file saved as PLAYEM.BAT- enter &ldquo;PLAYEM &gt;
nul 2&gt;&amp;1&rdquo; on the command line<span style="font-variant: normal"><font color="#242729"><font face="Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, sans-serif"><font size="2" style="font-size: 10pt"><span style="letter-spacing: normal"><span style="font-style: normal"><span style="font-weight: normal">.</span></span></span></font></font></font></span>
</p>
</body>
</html>
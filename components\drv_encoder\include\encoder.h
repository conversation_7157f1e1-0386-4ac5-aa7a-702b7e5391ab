#ifndef _ENCODER_H
#define _ENCODER_H

#include "driver/pulse_cnt.h"
#include "driver/gpio.h"
#include "esp_err.h"
#include "esp_log.h"

typedef enum
{
    ENCODER_KEY_STATE_REL = 0x00,
    ENCODER_KEY_STATE_PR = 0x01,
}encoder_key_state_t;

typedef struct
{
    int pulse_count;
    int high_limit;
    int low_limit;
    encoder_key_state_t key_state;
    pcnt_unit_handle_t pcnt_unit;
}encoder_t;

void drv_encoder_init(void);
encoder_t *drv_encoder_read(void);

#endif


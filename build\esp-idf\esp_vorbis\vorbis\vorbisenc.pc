# libvorbisenc pkg-config source file

prefix=C:/Program Files (x86)/DDClock
exec_prefix=C:/Program Files (x86)/DDClock/bin
libdir=C:/Program Files (x86)/DDClock/lib
includedir=C:/Program Files (x86)/DDClock/include

Name: vorbisenc
Description: vorbisenc is a library that provides a convenient API for setting up an encoding environment using libvorbis
Version: 1.3.7
Requires.private: vorbis
Conflicts:
Libs: -L${libdir} -lvorbisenc
Cflags: -I${includedir}

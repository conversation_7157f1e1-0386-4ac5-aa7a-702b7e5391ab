// l12 MPEG decoding tables
// output of:
// src/libmpg123/calctables l12

#if defined(RUNTIME_TABLES)

#ifdef REAL_IS_FLOAT

// aligned to 16 bytes for vector instructions, e.g. AltiVec

static ALIGNED(16) real layer12_table[27][64];

#endif

static unsigned char grp_3tab[96];

static unsigned char grp_5tab[384];

static unsigned char grp_9tab[3072];

#else

#ifdef REAL_IS_FLOAT

// aligned to 16 bytes for vector instructions, e.g. AltiVec

static const ALIGNED(16) real layer12_table[27][64] = 
{
	{
		 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	,	 0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f,  0.00000000e+00f
	}
,	{
		-1.33333333e+00f, -1.05826737e+00f, -8.39947367e-01f, -6.66666667e-01f
	,	-5.29133684e-01f, -4.19973683e-01f, -3.33333333e-01f, -2.64566842e-01f
	,	-2.09986842e-01f, -1.66666667e-01f, -1.32283421e-01f, -1.04993421e-01f
	,	-8.33333333e-02f, -6.61417105e-02f, -5.24967104e-02f, -4.16666667e-02f
	,	-3.30708552e-02f, -2.62483552e-02f, -2.08333333e-02f, -1.65354276e-02f
	,	-1.31241776e-02f, -1.04166667e-02f, -8.26771381e-03f, -6.56208880e-03f
	,	-5.20833333e-03f, -4.13385691e-03f, -3.28104440e-03f, -2.60416667e-03f
	,	-2.06692845e-03f, -1.64052220e-03f, -1.30208333e-03f, -1.03346423e-03f
	,	-8.20261100e-04f, -6.51041667e-04f, -5.16732113e-04f, -4.10130550e-04f
	,	-3.25520833e-04f, -2.58366057e-04f, -2.05065275e-04f, -1.62760417e-04f
	,	-1.29183028e-04f, -1.02532638e-04f, -8.13802083e-05f, -6.45915142e-05f
	,	-5.12663188e-05f, -4.06901042e-05f, -3.22957571e-05f, -2.56331594e-05f
	,	-2.03450521e-05f, -1.61478785e-05f, -1.28165797e-05f, -1.01725260e-05f
	,	-8.07393927e-06f, -6.40828985e-06f, -5.08626302e-06f, -4.03696963e-06f
	,	-3.20414492e-06f, -2.54313151e-06f, -2.01848482e-06f, -1.60207246e-06f
	,	-1.27156576e-06f, -1.00924241e-06f, -8.01036231e-07f,  0.00000000e+00f
	}
,	{
		 1.33333333e+00f,  1.05826737e+00f,  8.39947367e-01f,  6.66666667e-01f
	,	 5.29133684e-01f,  4.19973683e-01f,  3.33333333e-01f,  2.64566842e-01f
	,	 2.09986842e-01f,  1.66666667e-01f,  1.32283421e-01f,  1.04993421e-01f
	,	 8.33333333e-02f,  6.61417105e-02f,  5.24967104e-02f,  4.16666667e-02f
	,	 3.30708552e-02f,  2.62483552e-02f,  2.08333333e-02f,  1.65354276e-02f
	,	 1.31241776e-02f,  1.04166667e-02f,  8.26771381e-03f,  6.56208880e-03f
	,	 5.20833333e-03f,  4.13385691e-03f,  3.28104440e-03f,  2.60416667e-03f
	,	 2.06692845e-03f,  1.64052220e-03f,  1.30208333e-03f,  1.03346423e-03f
	,	 8.20261100e-04f,  6.51041667e-04f,  5.16732113e-04f,  4.10130550e-04f
	,	 3.25520833e-04f,  2.58366057e-04f,  2.05065275e-04f,  1.62760417e-04f
	,	 1.29183028e-04f,  1.02532638e-04f,  8.13802083e-05f,  6.45915142e-05f
	,	 5.12663188e-05f,  4.06901042e-05f,  3.22957571e-05f,  2.56331594e-05f
	,	 2.03450521e-05f,  1.61478785e-05f,  1.28165797e-05f,  1.01725260e-05f
	,	 8.07393927e-06f,  6.40828985e-06f,  5.08626302e-06f,  4.03696963e-06f
	,	 3.20414492e-06f,  2.54313151e-06f,  2.01848482e-06f,  1.60207246e-06f
	,	 1.27156576e-06f,  1.00924241e-06f,  8.01036231e-07f,  0.00000000e+00f
	}
,	{
		 5.71428571e-01f,  4.53543158e-01f,  3.59977443e-01f,  2.85714286e-01f
	,	 2.26771579e-01f,  1.79988721e-01f,  1.42857143e-01f,  1.13385789e-01f
	,	 8.99943607e-02f,  7.14285714e-02f,  5.66928947e-02f,  4.49971804e-02f
	,	 3.57142857e-02f,  2.83464474e-02f,  2.24985902e-02f,  1.78571429e-02f
	,	 1.41732237e-02f,  1.12492951e-02f,  8.92857143e-03f,  7.08661184e-03f
	,	 5.62464754e-03f,  4.46428571e-03f,  3.54330592e-03f,  2.81232377e-03f
	,	 2.23214286e-03f,  1.77165296e-03f,  1.40616189e-03f,  1.11607143e-03f
	,	 8.85826480e-04f,  7.03080943e-04f,  5.58035714e-04f,  4.42913240e-04f
	,	 3.51540472e-04f,  2.79017857e-04f,  2.21456620e-04f,  1.75770236e-04f
	,	 1.39508929e-04f,  1.10728310e-04f,  8.78851179e-05f,  6.97544643e-05f
	,	 5.53641550e-05f,  4.39425589e-05f,  3.48772321e-05f,  2.76820775e-05f
	,	 2.19712795e-05f,  1.74386161e-05f,  1.38410387e-05f,  1.09856397e-05f
	,	 8.71930804e-06f,  6.92051937e-06f,  5.49281987e-06f,  4.35965402e-06f
	,	 3.46025969e-06f,  2.74640993e-06f,  2.17982701e-06f,  1.73012984e-06f
	,	 1.37320497e-06f,  1.08991350e-06f,  8.65064922e-07f,  6.86602483e-07f
	,	 5.44956752e-07f,  4.32532461e-07f,  3.43301242e-07f,  0.00000000e+00f
	}
,	{
		 2.66666667e-01f,  2.11653474e-01f,  1.67989473e-01f,  1.33333333e-01f
	,	 1.05826737e-01f,  8.39947367e-02f,  6.66666667e-02f,  5.29133684e-02f
	,	 4.19973683e-02f,  3.33333333e-02f,  2.64566842e-02f,  2.09986842e-02f
	,	 1.66666667e-02f,  1.32283421e-02f,  1.04993421e-02f,  8.33333333e-03f
	,	 6.61417105e-03f,  5.24967104e-03f,  4.16666667e-03f,  3.30708552e-03f
	,	 2.62483552e-03f,  2.08333333e-03f,  1.65354276e-03f,  1.31241776e-03f
	,	 1.04166667e-03f,  8.26771381e-04f,  6.56208880e-04f,  5.20833333e-04f
	,	 4.13385691e-04f,  3.28104440e-04f,  2.60416667e-04f,  2.06692845e-04f
	,	 1.64052220e-04f,  1.30208333e-04f,  1.03346423e-04f,  8.20261100e-05f
	,	 6.51041667e-05f,  5.16732113e-05f,  4.10130550e-05f,  3.25520833e-05f
	,	 2.58366057e-05f,  2.05065275e-05f,  1.62760417e-05f,  1.29183028e-05f
	,	 1.02532638e-05f,  8.13802083e-06f,  6.45915142e-06f,  5.12663188e-06f
	,	 4.06901042e-06f,  3.22957571e-06f,  2.56331594e-06f,  2.03450521e-06f
	,	 1.61478785e-06f,  1.28165797e-06f,  1.01725260e-06f,  8.07393927e-07f
	,	 6.40828985e-07f,  5.08626302e-07f,  4.03696963e-07f,  3.20414492e-07f
	,	 2.54313151e-07f,  2.01848482e-07f,  1.60207246e-07f,  0.00000000e+00f
	}
,	{
		 1.29032258e-01f,  1.02412971e-01f,  8.12852290e-02f,  6.45161290e-02f
	,	 5.12064855e-02f,  4.06426145e-02f,  3.22580645e-02f,  2.56032428e-02f
	,	 2.03213073e-02f,  1.61290323e-02f,  1.28016214e-02f,  1.01606536e-02f
	,	 8.06451613e-03f,  6.40081069e-03f,  5.08032681e-03f,  4.03225806e-03f
	,	 3.20040535e-03f,  2.54016341e-03f,  2.01612903e-03f,  1.60020267e-03f
	,	 1.27008170e-03f,  1.00806452e-03f,  8.00101337e-04f,  6.35040852e-04f
	,	 5.04032258e-04f,  4.00050668e-04f,  3.17520426e-04f,  2.52016129e-04f
	,	 2.00025334e-04f,  1.58760213e-04f,  1.26008065e-04f,  1.00012667e-04f
	,	 7.93801065e-05f,  6.30040323e-05f,  5.00063335e-05f,  3.96900532e-05f
	,	 3.15020161e-05f,  2.50031668e-05f,  1.98450266e-05f,  1.57510081e-05f
	,	 1.25015834e-05f,  9.92251331e-06f,  7.87550403e-06f,  6.25079169e-06f
	,	 4.96125665e-06f,  3.93775202e-06f,  3.12539585e-06f,  2.48062833e-06f
	,	 1.96887601e-06f,  1.56269792e-06f,  1.24031416e-06f,  9.84438004e-07f
	,	 7.81348962e-07f,  6.20157082e-07f,  4.92219002e-07f,  3.90674481e-07f
	,	 3.10078541e-07f,  2.46109501e-07f,  1.95337240e-07f,  1.55039270e-07f
	,	 1.23054751e-07f,  9.76686202e-08f,  7.75196352e-08f,  0.00000000e+00f
	}
,	{
		 6.34920635e-02f,  5.03936842e-02f,  3.99974936e-02f,  3.17460317e-02f
	,	 2.51968421e-02f,  1.99987468e-02f,  1.58730159e-02f,  1.25984210e-02f
	,	 9.99937341e-03f,  7.93650794e-03f,  6.29921052e-03f,  4.99968671e-03f
	,	 3.96825397e-03f,  3.14960526e-03f,  2.49984335e-03f,  1.98412698e-03f
	,	 1.57480263e-03f,  1.24992168e-03f,  9.92063492e-04f,  7.87401315e-04f
	,	 6.24960838e-04f,  4.96031746e-04f,  3.93700658e-04f,  3.12480419e-04f
	,	 2.48015873e-04f,  1.96850329e-04f,  1.56240210e-04f,  1.24007937e-04f
	,	 9.84251644e-05f,  7.81201048e-05f,  6.20039683e-05f,  4.92125822e-05f
	,	 3.90600524e-05f,  3.10019841e-05f,  2.46062911e-05f,  1.95300262e-05f
	,	 1.55009921e-05f,  1.23031456e-05f,  9.76501310e-06f,  7.75049603e-06f
	,	 6.15157278e-06f,  4.88250655e-06f,  3.87524802e-06f,  3.07578639e-06f
	,	 2.44125327e-06f,  1.93762401e-06f,  1.53789319e-06f,  1.22062664e-06f
	,	 9.68812004e-07f,  7.68946597e-07f,  6.10313319e-07f,  4.84406002e-07f
	,	 3.84473299e-07f,  3.05156659e-07f,  2.42203001e-07f,  1.92236649e-07f
	,	 1.52578330e-07f,  1.21101500e-07f,  9.61183246e-08f,  7.62891648e-08f
	,	 6.05507502e-08f,  4.80591623e-08f,  3.81445824e-08f,  0.00000000e+00f
	}
,	{
		 3.14960630e-02f,  2.49984418e-02f,  1.98412764e-02f,  1.57480315e-02f
	,	 1.24992209e-02f,  9.92063819e-03f,  7.87401575e-03f,  6.24961044e-03f
	,	 4.96031909e-03f,  3.93700787e-03f,  3.12480522e-03f,  2.48015955e-03f
	,	 1.96850394e-03f,  1.56240261e-03f,  1.24007977e-03f,  9.84251969e-04f
	,	 7.81201305e-04f,  6.20039887e-04f,  4.92125984e-04f,  3.90600653e-04f
	,	 3.10019943e-04f,  2.46062992e-04f,  1.95300326e-04f,  1.55009972e-04f
	,	 1.23031496e-04f,  9.76501631e-05f,  7.75049858e-05f,  6.15157480e-05f
	,	 4.88250816e-05f,  3.87524929e-05f,  3.07578740e-05f,  2.44125408e-05f
	,	 1.93762465e-05f,  1.53789370e-05f,  1.22062704e-05f,  9.68812323e-06f
	,	 7.68946850e-06f,  6.10313520e-06f,  4.84406162e-06f,  3.84473425e-06f
	,	 3.05156760e-06f,  2.42203081e-06f,  1.92236713e-06f,  1.52578380e-06f
	,	 1.21101540e-06f,  9.61183563e-07f,  7.62891900e-07f,  6.05507702e-07f
	,	 4.80591781e-07f,  3.81445950e-07f,  3.02753851e-07f,  2.40295891e-07f
	,	 1.90722975e-07f,  1.51376925e-07f,  1.20147945e-07f,  9.53614874e-08f
	,	 7.56884627e-08f,  6.00739727e-08f,  4.76807437e-08f,  3.78442314e-08f
	,	 3.00369863e-08f,  2.38403719e-08f,  1.89221157e-08f,  0.00000000e+00f
	}
,	{
		 1.56862745e-02f,  1.24502043e-02f,  9.88173372e-03f,  7.84313725e-03f
	,	 6.22510216e-03f,  4.94086686e-03f,  3.92156863e-03f,  3.11255108e-03f
	,	 2.47043343e-03f,  1.96078431e-03f,  1.55627554e-03f,  1.23521672e-03f
	,	 9.80392157e-04f,  7.78137771e-04f,  6.17608358e-04f,  4.90196078e-04f
	,	 3.89068885e-04f,  3.08804179e-04f,  2.45098039e-04f,  1.94534443e-04f
	,	 1.54402089e-04f,  1.22549020e-04f,  9.72672213e-05f,  7.72010447e-05f
	,	 6.12745098e-05f,  4.86336107e-05f,  3.86005224e-05f,  3.06372549e-05f
	,	 2.43168053e-05f,  1.93002612e-05f,  1.53186275e-05f,  1.21584027e-05f
	,	 9.65013059e-06f,  7.65931373e-06f,  6.07920133e-06f,  4.82506530e-06f
	,	 3.82965686e-06f,  3.03960067e-06f,  2.41253265e-06f,  1.91482843e-06f
	,	 1.51980033e-06f,  1.20626632e-06f,  9.57414216e-07f,  7.59900167e-07f
	,	 6.03133162e-07f,  4.78707108e-07f,  3.79950083e-07f,  3.01566581e-07f
	,	 2.39353554e-07f,  1.89975042e-07f,  1.50783290e-07f,  1.19676777e-07f
	,	 9.49875208e-08f,  7.53916452e-08f,  5.98383885e-08f,  4.74937604e-08f
	,	 3.76958226e-08f,  2.99191942e-08f,  2.37468802e-08f,  1.88479113e-08f
	,	 1.49595971e-08f,  1.18734401e-08f,  9.42395565e-09f,  0.00000000e+00f
	}
,	{
		 7.82778865e-03f,  6.21291997e-03f,  4.93119785e-03f,  3.91389432e-03f
	,	 3.10645998e-03f,  2.46559892e-03f,  1.95694716e-03f,  1.55322999e-03f
	,	 1.23279946e-03f,  9.78473581e-04f,  7.76614996e-04f,  6.16399731e-04f
	,	 4.89236791e-04f,  3.88307498e-04f,  3.08199865e-04f,  2.44618395e-04f
	,	 1.94153749e-04f,  1.54099933e-04f,  1.22309198e-04f,  9.70768745e-05f
	,	 7.70499664e-05f,  6.11545988e-05f,  4.85384373e-05f,  3.85249832e-05f
	,	 3.05772994e-05f,  2.42692186e-05f,  1.92624916e-05f,  1.52886497e-05f
	,	 1.21346093e-05f,  9.63124579e-06f,  7.64432485e-06f,  6.06730466e-06f
	,	 4.81562290e-06f,  3.82216243e-06f,  3.03365233e-06f,  2.40781145e-06f
	,	 1.91108121e-06f,  1.51682616e-06f,  1.20390572e-06f,  9.55540607e-07f
	,	 7.58413082e-07f,  6.01952862e-07f,  4.77770303e-07f,  3.79206541e-07f
	,	 3.00976431e-07f,  2.38885152e-07f,  1.89603271e-07f,  1.50488216e-07f
	,	 1.19442576e-07f,  9.48016353e-08f,  7.52441078e-08f,  5.97212879e-08f
	,	 4.74008176e-08f,  3.76220539e-08f,  2.98606440e-08f,  2.37004088e-08f
	,	 1.88110269e-08f,  1.49303220e-08f,  1.18502044e-08f,  9.40551347e-09f
	,	 7.46516099e-09f,  5.92510220e-09f,  4.70275674e-09f,  0.00000000e+00f
	}
,	{
		 3.91006843e-03f,  3.10342337e-03f,  2.46318876e-03f,  1.95503421e-03f
	,	 1.55171168e-03f,  1.23159438e-03f,  9.77517107e-04f,  7.75855842e-04f
	,	 6.15797190e-04f,  4.88758553e-04f,  3.87927921e-04f,  3.07898595e-04f
	,	 2.44379277e-04f,  1.93963960e-04f,  1.53949297e-04f,  1.22189638e-04f
	,	 9.69819802e-05f,  7.69746487e-05f,  6.10948192e-05f,  4.84909901e-05f
	,	 3.84873243e-05f,  3.05474096e-05f,  2.42454951e-05f,  1.92436622e-05f
	,	 1.52737048e-05f,  1.21227475e-05f,  9.62183109e-06f,  7.63685239e-06f
	,	 6.06137376e-06f,  4.81091554e-06f,  3.81842620e-06f,  3.03068688e-06f
	,	 2.40545777e-06f,  1.90921310e-06f,  1.51534344e-06f,  1.20272889e-06f
	,	 9.54606549e-07f,  7.57671720e-07f,  6.01364443e-07f,  4.77303275e-07f
	,	 3.78835860e-07f,  3.00682221e-07f,  2.38651637e-07f,  1.89417930e-07f
	,	 1.50341111e-07f,  1.19325819e-07f,  9.47089650e-08f,  7.51705554e-08f
	,	 5.96629093e-08f,  4.73544825e-08f,  3.75852777e-08f,  2.98314547e-08f
	,	 2.36772413e-08f,  1.87926388e-08f,  1.49157273e-08f,  1.18386206e-08f
	,	 9.39631942e-09f,  7.45786367e-09f,  5.91931032e-09f,  4.69815971e-09f
	,	 3.72893183e-09f,  2.95965516e-09f,  2.34907986e-09f,  0.00000000e+00f
	}
,	{
		 1.95407914e-03f,  1.55095364e-03f,  1.23099272e-03f,  9.77039570e-04f
	,	 7.75476821e-04f,  6.15496360e-04f,  4.88519785e-04f,  3.87738410e-04f
	,	 3.07748180e-04f,  2.44259893e-04f,  1.93869205e-04f,  1.53874090e-04f
	,	 1.22129946e-04f,  9.69346026e-05f,  7.69370451e-05f,  6.10649731e-05f
	,	 4.84673013e-05f,  3.84685225e-05f,  3.05324866e-05f,  2.42336506e-05f
	,	 1.92342613e-05f,  1.52662433e-05f,  1.21168253e-05f,  9.61713063e-06f
	,	 7.63312164e-06f,  6.05841266e-06f,  4.80856532e-06f,  3.81656082e-06f
	,	 3.02920633e-06f,  2.40428266e-06f,  1.90828041e-06f,  1.51460317e-06f
	,	 1.20214133e-06f,  9.54140205e-07f,  7.57301583e-07f,  6.01070665e-07f
	,	 4.77070103e-07f,  3.78650791e-07f,  3.00535332e-07f,  2.38535051e-07f
	,	 1.89325396e-07f,  1.50267666e-07f,  1.19267526e-07f,  9.46626978e-08f
	,	 7.51338331e-08f,  5.96337628e-08f,  4.73313489e-08f,  3.75669165e-08f
	,	 2.98168814e-08f,  2.36656745e-08f,  1.87834583e-08f,  1.49084407e-08f
	,	 1.18328372e-08f,  9.39172913e-09f,  7.45422035e-09f,  5.91641861e-09f
	,	 4.69586457e-09f,  3.72711018e-09f,  2.95820931e-09f,  2.34793228e-09f
	,	 1.86355509e-09f,  1.47910465e-09f,  1.17396614e-09f,  0.00000000e+00f
	}
,	{
		 9.76800977e-04f,  7.75287449e-04f,  6.15346056e-04f,  4.88400488e-04f
	,	 3.87643725e-04f,  3.07673028e-04f,  2.44200244e-04f,  1.93821862e-04f
	,	 1.53836514e-04f,  1.22100122e-04f,  9.69109311e-05f,  7.69182570e-05f
	,	 6.10500611e-05f,  4.84554656e-05f,  3.84591285e-05f,  3.05250305e-05f
	,	 2.42277328e-05f,  1.92295643e-05f,  1.52625153e-05f,  1.21138664e-05f
	,	 9.61478213e-06f,  7.63125763e-06f,  6.05693320e-06f,  4.80739106e-06f
	,	 3.81562882e-06f,  3.02846660e-06f,  2.40369553e-06f,  1.90781441e-06f
	,	 1.51423330e-06f,  1.20184777e-06f,  9.53907204e-07f,  7.57116649e-07f
	,	 6.00923883e-07f,  4.76953602e-07f,  3.78558325e-07f,  3.00461941e-07f
	,	 2.38476801e-07f,  1.89279162e-07f,  1.50230971e-07f,  1.19238400e-07f
	,	 9.46395812e-08f,  7.51154854e-08f,  5.96192002e-08f,  4.73197906e-08f
	,	 3.75577427e-08f,  2.98096001e-08f,  2.36598953e-08f,  1.87788713e-08f
	,	 1.49048001e-08f,  1.18299476e-08f,  9.38943567e-09f,  7.45240003e-09f
	,	 5.91497382e-09f,  4.69471784e-09f,  3.72620002e-09f,  2.95748691e-09f
	,	 2.34735892e-09f,  1.86310001e-09f,  1.47874346e-09f,  1.17367946e-09f
	,	 9.31550004e-10f,  7.39371728e-10f,  5.86839729e-10f,  0.00000000e+00f
	}
,	{
		 4.88340862e-04f,  3.87596399e-04f,  3.07635466e-04f,  2.44170431e-04f
	,	 1.93798199e-04f,  1.53817733e-04f,  1.22085215e-04f,  9.68990997e-05f
	,	 7.69088664e-05f,  6.10426077e-05f,  4.84495499e-05f,  3.84544332e-05f
	,	 3.05213039e-05f,  2.42247749e-05f,  1.92272166e-05f,  1.52606519e-05f
	,	 1.21123875e-05f,  9.61360830e-06f,  7.63032597e-06f,  6.05619373e-06f
	,	 4.80680415e-06f,  3.81516298e-06f,  3.02809687e-06f,  2.40340208e-06f
	,	 1.90758149e-06f,  1.51404843e-06f,  1.20170104e-06f,  9.53790746e-07f
	,	 7.57024217e-07f,  6.00850519e-07f,  4.76895373e-07f,  3.78512108e-07f
	,	 3.00425260e-07f,  2.38447686e-07f,  1.89256054e-07f,  1.50212630e-07f
	,	 1.19223843e-07f,  9.46280271e-08f,  7.51063149e-08f,  5.96119216e-08f
	,	 4.73140135e-08f,  3.75531574e-08f,  2.98059608e-08f,  2.36570068e-08f
	,	 1.87765787e-08f,  1.49029804e-08f,  1.18285034e-08f,  9.38828936e-09f
	,	 7.45149020e-09f,  5.91425169e-09f,  4.69414468e-09f,  3.72574510e-09f
	,	 2.95712585e-09f,  2.34707234e-09f,  1.86287255e-09f,  1.47856292e-09f
	,	 1.17353617e-09f,  9.31436275e-10f,  7.39281462e-10f,  5.86768085e-10f
	,	 4.65718138e-10f,  3.69640731e-10f,  2.93384042e-10f,  0.00000000e+00f
	}
,	{
		 2.44155527e-04f,  1.93786370e-04f,  1.53808344e-04f,  1.22077764e-04f
	,	 9.68931851e-05f,  7.69041720e-05f,  6.10388818e-05f,  4.84465926e-05f
	,	 3.84520860e-05f,  3.05194409e-05f,  2.42232963e-05f,  1.92260430e-05f
	,	 1.52597204e-05f,  1.21116481e-05f,  9.61302150e-06f,  7.62986022e-06f
	,	 6.05582407e-06f,  4.80651075e-06f,  3.81493011e-06f,  3.02791204e-06f
	,	 2.40325538e-06f,  1.90746506e-06f,  1.51395602e-06f,  1.20162769e-06f
	,	 9.53732528e-07f,  7.56978009e-07f,  6.00813844e-07f,  4.76866264e-07f
	,	 3.78489004e-07f,  3.00406922e-07f,  2.38433132e-07f,  1.89244502e-07f
	,	 1.50203461e-07f,  1.19216566e-07f,  9.46222511e-08f,  7.51017305e-08f
	,	 5.96082830e-08f,  4.73111256e-08f,  3.75508652e-08f,  2.98041415e-08f
	,	 2.36555628e-08f,  1.87754326e-08f,  1.49020707e-08f,  1.18277814e-08f
	,	 9.38771631e-09f,  7.45103537e-09f,  5.91389069e-09f,  4.69385815e-09f
	,	 3.72551769e-09f,  2.95694535e-09f,  2.34692908e-09f,  1.86275884e-09f
	,	 1.47847267e-09f,  1.17346454e-09f,  9.31379422e-10f,  7.39236337e-10f
	,	 5.86732269e-10f,  4.65689711e-10f,  3.69618168e-10f,  2.93366135e-10f
	,	 2.32844855e-10f,  1.84809084e-10f,  1.46683067e-10f,  0.00000000e+00f
	}
,	{
		 1.22074038e-04f,  9.68902281e-05f,  7.69018250e-05f,  6.10370190e-05f
	,	 4.84451140e-05f,  3.84509125e-05f,  3.05185095e-05f,  2.42225570e-05f
	,	 1.92254563e-05f,  1.52592547e-05f,  1.21112785e-05f,  9.61272813e-06f
	,	 7.62962737e-06f,  6.05563926e-06f,  4.80636406e-06f,  3.81481368e-06f
	,	 3.02781963e-06f,  2.40318203e-06f,  1.90740684e-06f,  1.51390981e-06f
	,	 1.20159102e-06f,  9.53703421e-07f,  7.56954907e-07f,  6.00795508e-07f
	,	 4.76851711e-07f,  3.78477453e-07f,  3.00397754e-07f,  2.38425855e-07f
	,	 1.89238727e-07f,  1.50198877e-07f,  1.19212928e-07f,  9.46193634e-08f
	,	 7.50994385e-08f,  5.96064638e-08f,  4.73096817e-08f,  3.75497192e-08f
	,	 2.98032319e-08f,  2.36548408e-08f,  1.87748596e-08f,  1.49016160e-08f
	,	 1.18274204e-08f,  9.38742981e-09f,  7.45080798e-09f,  5.91371021e-09f
	,	 4.69371490e-09f,  3.72540399e-09f,  2.95685511e-09f,  2.34685745e-09f
	,	 1.86270199e-09f,  1.47842755e-09f,  1.17342873e-09f,  9.31350997e-10f
	,	 7.39213776e-10f,  5.86714363e-10f,  4.65675499e-10f,  3.69606888e-10f
	,	 2.93357182e-10f,  2.32837749e-10f,  1.84803444e-10f,  1.46678591e-10f
	,	 1.16418875e-10f,  9.24017220e-11f,  7.33392954e-11f,  0.00000000e+00f
	}
,	{
		 6.10360876e-05f,  4.84443748e-05f,  3.84503258e-05f,  3.05180438e-05f
	,	 2.42221874e-05f,  1.92251629e-05f,  1.52590219e-05f,  1.21110937e-05f
	,	 9.61258144e-06f,  7.62951095e-06f,  6.05554685e-06f,  4.80629072e-06f
	,	 3.81475547e-06f,  3.02777343e-06f,  2.40314536e-06f,  1.90737774e-06f
	,	 1.51388671e-06f,  1.20157268e-06f,  9.53688869e-07f,  7.56943357e-07f
	,	 6.00786340e-07f,  4.76844434e-07f,  3.78471678e-07f,  3.00393170e-07f
	,	 2.38422217e-07f,  1.89235839e-07f,  1.50196585e-07f,  1.19211109e-07f
	,	 9.46179196e-08f,  7.50982925e-08f,  5.96055543e-08f,  4.73089598e-08f
	,	 3.75491463e-08f,  2.98027771e-08f,  2.36544799e-08f,  1.87745731e-08f
	,	 1.49013886e-08f,  1.18272399e-08f,  9.38728657e-09f,  7.45069429e-09f
	,	 5.91361997e-09f,  4.69364328e-09f,  3.72534714e-09f,  2.95680999e-09f
	,	 2.34682164e-09f,  1.86267357e-09f,  1.47840499e-09f,  1.17341082e-09f
	,	 9.31336786e-10f,  7.39202497e-10f,  5.86705410e-10f,  4.65668393e-10f
	,	 3.69601248e-10f,  2.93352705e-10f,  2.32834196e-10f,  1.84800624e-10f
	,	 1.46676353e-10f,  1.16417098e-10f,  9.24003121e-11f,  7.33381763e-11f
	,	 5.82085491e-11f,  4.62001560e-11f,  3.66690882e-11f,  0.00000000e+00f
	}
,	{
		-1.60000000e+00f, -1.26992084e+00f, -1.00793684e+00f, -8.00000000e-01f
	,	-6.34960421e-01f, -5.03968420e-01f, -4.00000000e-01f, -3.17480210e-01f
	,	-2.51984210e-01f, -2.00000000e-01f, -1.58740105e-01f, -1.25992105e-01f
	,	-1.00000000e-01f, -7.93700526e-02f, -6.29960525e-02f, -5.00000000e-02f
	,	-3.96850263e-02f, -3.14980262e-02f, -2.50000000e-02f, -1.98425131e-02f
	,	-1.57490131e-02f, -1.25000000e-02f, -9.92125657e-03f, -7.87450656e-03f
	,	-6.25000000e-03f, -4.96062829e-03f, -3.93725328e-03f, -3.12500000e-03f
	,	-2.48031414e-03f, -1.96862664e-03f, -1.56250000e-03f, -1.24015707e-03f
	,	-9.84313320e-04f, -7.81250000e-04f, -6.20078536e-04f, -4.92156660e-04f
	,	-3.90625000e-04f, -3.10039268e-04f, -2.46078330e-04f, -1.95312500e-04f
	,	-1.55019634e-04f, -1.23039165e-04f, -9.76562500e-05f, -7.75098170e-05f
	,	-6.15195825e-05f, -4.88281250e-05f, -3.87549085e-05f, -3.07597913e-05f
	,	-2.44140625e-05f, -1.93774542e-05f, -1.53798956e-05f, -1.22070313e-05f
	,	-9.68872712e-06f, -7.68994781e-06f, -6.10351563e-06f, -4.84436356e-06f
	,	-3.84497391e-06f, -3.05175781e-06f, -2.42218178e-06f, -1.92248695e-06f
	,	-1.52587891e-06f, -1.21109089e-06f, -9.61243477e-07f,  0.00000000e+00f
	}
,	{
		-8.00000000e-01f, -6.34960421e-01f, -5.03968420e-01f, -4.00000000e-01f
	,	-3.17480210e-01f, -2.51984210e-01f, -2.00000000e-01f, -1.58740105e-01f
	,	-1.25992105e-01f, -1.00000000e-01f, -7.93700526e-02f, -6.29960525e-02f
	,	-5.00000000e-02f, -3.96850263e-02f, -3.14980262e-02f, -2.50000000e-02f
	,	-1.98425131e-02f, -1.57490131e-02f, -1.25000000e-02f, -9.92125657e-03f
	,	-7.87450656e-03f, -6.25000000e-03f, -4.96062829e-03f, -3.93725328e-03f
	,	-3.12500000e-03f, -2.48031414e-03f, -1.96862664e-03f, -1.56250000e-03f
	,	-1.24015707e-03f, -9.84313320e-04f, -7.81250000e-04f, -6.20078536e-04f
	,	-4.92156660e-04f, -3.90625000e-04f, -3.10039268e-04f, -2.46078330e-04f
	,	-1.95312500e-04f, -1.55019634e-04f, -1.23039165e-04f, -9.76562500e-05f
	,	-7.75098170e-05f, -6.15195825e-05f, -4.88281250e-05f, -3.87549085e-05f
	,	-3.07597913e-05f, -2.44140625e-05f, -1.93774542e-05f, -1.53798956e-05f
	,	-1.22070313e-05f, -9.68872712e-06f, -7.68994781e-06f, -6.10351563e-06f
	,	-4.84436356e-06f, -3.84497391e-06f, -3.05175781e-06f, -2.42218178e-06f
	,	-1.92248695e-06f, -1.52587891e-06f, -1.21109089e-06f, -9.61243477e-07f
	,	-7.62939453e-07f, -6.05545445e-07f, -4.80621738e-07f,  0.00000000e+00f
	}
,	{
		 8.00000000e-01f,  6.34960421e-01f,  5.03968420e-01f,  4.00000000e-01f
	,	 3.17480210e-01f,  2.51984210e-01f,  2.00000000e-01f,  1.58740105e-01f
	,	 1.25992105e-01f,  1.00000000e-01f,  7.93700526e-02f,  6.29960525e-02f
	,	 5.00000000e-02f,  3.96850263e-02f,  3.14980262e-02f,  2.50000000e-02f
	,	 1.98425131e-02f,  1.57490131e-02f,  1.25000000e-02f,  9.92125657e-03f
	,	 7.87450656e-03f,  6.25000000e-03f,  4.96062829e-03f,  3.93725328e-03f
	,	 3.12500000e-03f,  2.48031414e-03f,  1.96862664e-03f,  1.56250000e-03f
	,	 1.24015707e-03f,  9.84313320e-04f,  7.81250000e-04f,  6.20078536e-04f
	,	 4.92156660e-04f,  3.90625000e-04f,  3.10039268e-04f,  2.46078330e-04f
	,	 1.95312500e-04f,  1.55019634e-04f,  1.23039165e-04f,  9.76562500e-05f
	,	 7.75098170e-05f,  6.15195825e-05f,  4.88281250e-05f,  3.87549085e-05f
	,	 3.07597913e-05f,  2.44140625e-05f,  1.93774542e-05f,  1.53798956e-05f
	,	 1.22070313e-05f,  9.68872712e-06f,  7.68994781e-06f,  6.10351563e-06f
	,	 4.84436356e-06f,  3.84497391e-06f,  3.05175781e-06f,  2.42218178e-06f
	,	 1.92248695e-06f,  1.52587891e-06f,  1.21109089e-06f,  9.61243477e-07f
	,	 7.62939453e-07f,  6.05545445e-07f,  4.80621738e-07f,  0.00000000e+00f
	}
,	{
		 1.60000000e+00f,  1.26992084e+00f,  1.00793684e+00f,  8.00000000e-01f
	,	 6.34960421e-01f,  5.03968420e-01f,  4.00000000e-01f,  3.17480210e-01f
	,	 2.51984210e-01f,  2.00000000e-01f,  1.58740105e-01f,  1.25992105e-01f
	,	 1.00000000e-01f,  7.93700526e-02f,  6.29960525e-02f,  5.00000000e-02f
	,	 3.96850263e-02f,  3.14980262e-02f,  2.50000000e-02f,  1.98425131e-02f
	,	 1.57490131e-02f,  1.25000000e-02f,  9.92125657e-03f,  7.87450656e-03f
	,	 6.25000000e-03f,  4.96062829e-03f,  3.93725328e-03f,  3.12500000e-03f
	,	 2.48031414e-03f,  1.96862664e-03f,  1.56250000e-03f,  1.24015707e-03f
	,	 9.84313320e-04f,  7.81250000e-04f,  6.20078536e-04f,  4.92156660e-04f
	,	 3.90625000e-04f,  3.10039268e-04f,  2.46078330e-04f,  1.95312500e-04f
	,	 1.55019634e-04f,  1.23039165e-04f,  9.76562500e-05f,  7.75098170e-05f
	,	 6.15195825e-05f,  4.88281250e-05f,  3.87549085e-05f,  3.07597913e-05f
	,	 2.44140625e-05f,  1.93774542e-05f,  1.53798956e-05f,  1.22070313e-05f
	,	 9.68872712e-06f,  7.68994781e-06f,  6.10351563e-06f,  4.84436356e-06f
	,	 3.84497391e-06f,  3.05175781e-06f,  2.42218178e-06f,  1.92248695e-06f
	,	 1.52587891e-06f,  1.21109089e-06f,  9.61243477e-07f,  0.00000000e+00f
	}
,	{
		-1.77777778e+00f, -1.41102316e+00f, -1.11992982e+00f, -8.88888889e-01f
	,	-7.05511579e-01f, -5.59964911e-01f, -4.44444444e-01f, -3.52755789e-01f
	,	-2.79982456e-01f, -2.22222222e-01f, -1.76377895e-01f, -1.39991228e-01f
	,	-1.11111111e-01f, -8.81889473e-02f, -6.99956139e-02f, -5.55555556e-02f
	,	-4.40944737e-02f, -3.49978069e-02f, -2.77777778e-02f, -2.20472368e-02f
	,	-1.74989035e-02f, -1.38888889e-02f, -1.10236184e-02f, -8.74945174e-03f
	,	-6.94444444e-03f, -5.51180921e-03f, -4.37472587e-03f, -3.47222222e-03f
	,	-2.75590460e-03f, -2.18736293e-03f, -1.73611111e-03f, -1.37795230e-03f
	,	-1.09368147e-03f, -8.68055556e-04f, -6.88976151e-04f, -5.46840733e-04f
	,	-4.34027778e-04f, -3.44488076e-04f, -2.73420367e-04f, -2.17013889e-04f
	,	-1.72244038e-04f, -1.36710183e-04f, -1.08506944e-04f, -8.61220189e-05f
	,	-6.83550917e-05f, -5.42534722e-05f, -4.30610094e-05f, -3.41775458e-05f
	,	-2.71267361e-05f, -2.15305047e-05f, -1.70887729e-05f, -1.35633681e-05f
	,	-1.07652524e-05f, -8.54438646e-06f, -6.78168403e-06f, -5.38262618e-06f
	,	-4.27219323e-06f, -3.39084201e-06f, -2.69131309e-06f, -2.13609662e-06f
	,	-1.69542101e-06f, -1.34565654e-06f, -1.06804831e-06f,  0.00000000e+00f
	}
,	{
		-8.88888889e-01f, -7.05511579e-01f, -5.59964911e-01f, -4.44444444e-01f
	,	-3.52755789e-01f, -2.79982456e-01f, -2.22222222e-01f, -1.76377895e-01f
	,	-1.39991228e-01f, -1.11111111e-01f, -8.81889473e-02f, -6.99956139e-02f
	,	-5.55555556e-02f, -4.40944737e-02f, -3.49978069e-02f, -2.77777778e-02f
	,	-2.20472368e-02f, -1.74989035e-02f, -1.38888889e-02f, -1.10236184e-02f
	,	-8.74945174e-03f, -6.94444444e-03f, -5.51180921e-03f, -4.37472587e-03f
	,	-3.47222222e-03f, -2.75590460e-03f, -2.18736293e-03f, -1.73611111e-03f
	,	-1.37795230e-03f, -1.09368147e-03f, -8.68055556e-04f, -6.88976151e-04f
	,	-5.46840733e-04f, -4.34027778e-04f, -3.44488076e-04f, -2.73420367e-04f
	,	-2.17013889e-04f, -1.72244038e-04f, -1.36710183e-04f, -1.08506944e-04f
	,	-8.61220189e-05f, -6.83550917e-05f, -5.42534722e-05f, -4.30610094e-05f
	,	-3.41775458e-05f, -2.71267361e-05f, -2.15305047e-05f, -1.70887729e-05f
	,	-1.35633681e-05f, -1.07652524e-05f, -8.54438646e-06f, -6.78168403e-06f
	,	-5.38262618e-06f, -4.27219323e-06f, -3.39084201e-06f, -2.69131309e-06f
	,	-2.13609662e-06f, -1.69542101e-06f, -1.34565654e-06f, -1.06804831e-06f
	,	-8.47710503e-07f, -6.72828272e-07f, -5.34024154e-07f,  0.00000000e+00f
	}
,	{
		-4.44444444e-01f, -3.52755789e-01f, -2.79982456e-01f, -2.22222222e-01f
	,	-1.76377895e-01f, -1.39991228e-01f, -1.11111111e-01f, -8.81889473e-02f
	,	-6.99956139e-02f, -5.55555556e-02f, -4.40944737e-02f, -3.49978069e-02f
	,	-2.77777778e-02f, -2.20472368e-02f, -1.74989035e-02f, -1.38888889e-02f
	,	-1.10236184e-02f, -8.74945174e-03f, -6.94444444e-03f, -5.51180921e-03f
	,	-4.37472587e-03f, -3.47222222e-03f, -2.75590460e-03f, -2.18736293e-03f
	,	-1.73611111e-03f, -1.37795230e-03f, -1.09368147e-03f, -8.68055556e-04f
	,	-6.88976151e-04f, -5.46840733e-04f, -4.34027778e-04f, -3.44488076e-04f
	,	-2.73420367e-04f, -2.17013889e-04f, -1.72244038e-04f, -1.36710183e-04f
	,	-1.08506944e-04f, -8.61220189e-05f, -6.83550917e-05f, -5.42534722e-05f
	,	-4.30610094e-05f, -3.41775458e-05f, -2.71267361e-05f, -2.15305047e-05f
	,	-1.70887729e-05f, -1.35633681e-05f, -1.07652524e-05f, -8.54438646e-06f
	,	-6.78168403e-06f, -5.38262618e-06f, -4.27219323e-06f, -3.39084201e-06f
	,	-2.69131309e-06f, -2.13609662e-06f, -1.69542101e-06f, -1.34565654e-06f
	,	-1.06804831e-06f, -8.47710503e-07f, -6.72828272e-07f, -5.34024154e-07f
	,	-4.23855252e-07f, -3.36414136e-07f, -2.67012077e-07f,  0.00000000e+00f
	}
,	{
		 4.44444444e-01f,  3.52755789e-01f,  2.79982456e-01f,  2.22222222e-01f
	,	 1.76377895e-01f,  1.39991228e-01f,  1.11111111e-01f,  8.81889473e-02f
	,	 6.99956139e-02f,  5.55555556e-02f,  4.40944737e-02f,  3.49978069e-02f
	,	 2.77777778e-02f,  2.20472368e-02f,  1.74989035e-02f,  1.38888889e-02f
	,	 1.10236184e-02f,  8.74945174e-03f,  6.94444444e-03f,  5.51180921e-03f
	,	 4.37472587e-03f,  3.47222222e-03f,  2.75590460e-03f,  2.18736293e-03f
	,	 1.73611111e-03f,  1.37795230e-03f,  1.09368147e-03f,  8.68055556e-04f
	,	 6.88976151e-04f,  5.46840733e-04f,  4.34027778e-04f,  3.44488076e-04f
	,	 2.73420367e-04f,  2.17013889e-04f,  1.72244038e-04f,  1.36710183e-04f
	,	 1.08506944e-04f,  8.61220189e-05f,  6.83550917e-05f,  5.42534722e-05f
	,	 4.30610094e-05f,  3.41775458e-05f,  2.71267361e-05f,  2.15305047e-05f
	,	 1.70887729e-05f,  1.35633681e-05f,  1.07652524e-05f,  8.54438646e-06f
	,	 6.78168403e-06f,  5.38262618e-06f,  4.27219323e-06f,  3.39084201e-06f
	,	 2.69131309e-06f,  2.13609662e-06f,  1.69542101e-06f,  1.34565654e-06f
	,	 1.06804831e-06f,  8.47710503e-07f,  6.72828272e-07f,  5.34024154e-07f
	,	 4.23855252e-07f,  3.36414136e-07f,  2.67012077e-07f,  0.00000000e+00f
	}
,	{
		 8.88888889e-01f,  7.05511579e-01f,  5.59964911e-01f,  4.44444444e-01f
	,	 3.52755789e-01f,  2.79982456e-01f,  2.22222222e-01f,  1.76377895e-01f
	,	 1.39991228e-01f,  1.11111111e-01f,  8.81889473e-02f,  6.99956139e-02f
	,	 5.55555556e-02f,  4.40944737e-02f,  3.49978069e-02f,  2.77777778e-02f
	,	 2.20472368e-02f,  1.74989035e-02f,  1.38888889e-02f,  1.10236184e-02f
	,	 8.74945174e-03f,  6.94444444e-03f,  5.51180921e-03f,  4.37472587e-03f
	,	 3.47222222e-03f,  2.75590460e-03f,  2.18736293e-03f,  1.73611111e-03f
	,	 1.37795230e-03f,  1.09368147e-03f,  8.68055556e-04f,  6.88976151e-04f
	,	 5.46840733e-04f,  4.34027778e-04f,  3.44488076e-04f,  2.73420367e-04f
	,	 2.17013889e-04f,  1.72244038e-04f,  1.36710183e-04f,  1.08506944e-04f
	,	 8.61220189e-05f,  6.83550917e-05f,  5.42534722e-05f,  4.30610094e-05f
	,	 3.41775458e-05f,  2.71267361e-05f,  2.15305047e-05f,  1.70887729e-05f
	,	 1.35633681e-05f,  1.07652524e-05f,  8.54438646e-06f,  6.78168403e-06f
	,	 5.38262618e-06f,  4.27219323e-06f,  3.39084201e-06f,  2.69131309e-06f
	,	 2.13609662e-06f,  1.69542101e-06f,  1.34565654e-06f,  1.06804831e-06f
	,	 8.47710503e-07f,  6.72828272e-07f,  5.34024154e-07f,  0.00000000e+00f
	}
,	{
		 1.77777778e+00f,  1.41102316e+00f,  1.11992982e+00f,  8.88888889e-01f
	,	 7.05511579e-01f,  5.59964911e-01f,  4.44444444e-01f,  3.52755789e-01f
	,	 2.79982456e-01f,  2.22222222e-01f,  1.76377895e-01f,  1.39991228e-01f
	,	 1.11111111e-01f,  8.81889473e-02f,  6.99956139e-02f,  5.55555556e-02f
	,	 4.40944737e-02f,  3.49978069e-02f,  2.77777778e-02f,  2.20472368e-02f
	,	 1.74989035e-02f,  1.38888889e-02f,  1.10236184e-02f,  8.74945174e-03f
	,	 6.94444444e-03f,  5.51180921e-03f,  4.37472587e-03f,  3.47222222e-03f
	,	 2.75590460e-03f,  2.18736293e-03f,  1.73611111e-03f,  1.37795230e-03f
	,	 1.09368147e-03f,  8.68055556e-04f,  6.88976151e-04f,  5.46840733e-04f
	,	 4.34027778e-04f,  3.44488076e-04f,  2.73420367e-04f,  2.17013889e-04f
	,	 1.72244038e-04f,  1.36710183e-04f,  1.08506944e-04f,  8.61220189e-05f
	,	 6.83550917e-05f,  5.42534722e-05f,  4.30610094e-05f,  3.41775458e-05f
	,	 2.71267361e-05f,  2.15305047e-05f,  1.70887729e-05f,  1.35633681e-05f
	,	 1.07652524e-05f,  8.54438646e-06f,  6.78168403e-06f,  5.38262618e-06f
	,	 4.27219323e-06f,  3.39084201e-06f,  2.69131309e-06f,  2.13609662e-06f
	,	 1.69542101e-06f,  1.34565654e-06f,  1.06804831e-06f,  0.00000000e+00f
	}
};

#endif

#ifdef REAL_IS_FIXED

static const real layer12_table[27][64] = 
{
	{
		          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	}
,	{
		-1431655765, -1136305934,  -901886617,  -715827883
	,	 -568152967,  -450943309,  -357913941,  -284076483
	,	 -225471654,  -178956971,  -142038242,  -112735827
	,	  -89478485,   -71019121,   -56367914,   -44739243
	,	  -35509560,   -28183957,   -22369621,   -17754780
	,	  -14091978,   -11184811,    -8877390,    -7045989
	,	   -5592405,    -4438695,    -3522995,    -2796203
	,	   -2219348,    -1761497,    -1398101,    -1109674
	,	    -880749,     -699051,     -554837,     -440374
	,	    -349525,     -277418,     -220187,     -174763
	,	    -138709,     -110094,      -87381,      -69355
	,	     -55047,      -43691,      -34677,      -27523
	,	     -21845,      -17339,      -13762,      -10923
	,	      -8669,       -6881,       -5461,       -4335
	,	      -3440,       -2731,       -2167,       -1720
	,	      -1365,       -1084,        -860,           0
	}
,	{
		 1431655765,  1136305934,   901886617,   715827883
	,	  568152967,   450943309,   357913941,   284076483
	,	  225471654,   178956971,   142038242,   112735827
	,	   89478485,    71019121,    56367914,    44739243
	,	   35509560,    28183957,    22369621,    17754780
	,	   14091978,    11184811,     8877390,     7045989
	,	    5592405,     4438695,     3522995,     2796203
	,	    2219348,     1761497,     1398101,     1109674
	,	     880749,      699051,      554837,      440374
	,	     349525,      277418,      220187,      174763
	,	     138709,      110094,       87381,       69355
	,	      55047,       43691,       34677,       27523
	,	      21845,       17339,       13762,       10923
	,	       8669,        6881,        5461,        4335
	,	       3440,        2731,        2167,        1720
	,	       1365,        1084,         860,           0
	}
,	{
		  613566757,   486988257,   386522836,   306783378
	,	  243494129,   193261418,   153391689,   121747064
	,	   96630709,    76695845,    60873532,    48315355
	,	   38347922,    30436766,    24157677,    19173961
	,	   15218383,    12078839,     9586981,     7609192
	,	    6039419,     4793490,     3804596,     3019710
	,	    2396745,     1902298,     1509855,     1198373
	,	     951149,      754927,      599186,      475574
	,	     377464,      299593,      237787,      188732
	,	     149797,      118894,       94366,       74898
	,	      59447,       47183,       37449,       29723
	,	      23591,       18725,       14862,       11796
	,	       9362,        7431,        5898,        4681
	,	       3715,        2949,        2341,        1858
	,	       1474,        1170,         929,         737
	,	        585,         464,         369,           0
	}
,	{
		  286331153,   227261187,   180377323,   143165577
	,	  113630593,    90188662,    71582788,    56815297
	,	   45094331,    35791394,    28407648,    22547165
	,	   17895697,    14203824,    11273583,     8947849
	,	    7101912,     5636791,     4473924,     3550956
	,	    2818396,     2236962,     1775478,     1409198
	,	    1118481,      887739,      704599,      559241
	,	     443870,      352299,      279620,      221935
	,	     176150,      139810,      110967,       88075
	,	      69905,       55484,       44037,       34953
	,	      27742,       22019,       17476,       13871
	,	      11009,        8738,        6935,        5505
	,	       4369,        3468,        2752,        2185
	,	       1734,        1376,        1092,         867
	,	        688,         546,         433,         344
	,	        273,         217,         172,           0
	}
,	{
		  138547332,   109965090,    87279350,    69273666
	,	   54982545,    43639675,    34636833,    27491273
	,	   21819838,    17318417,    13745636,    10909919
	,	    8659208,     6872818,     5454959,     4329604
	,	    3436409,     2727480,     2164802,     1718205
	,	    1363740,     1082401,      859102,      681870
	,	     541201,      429551,      340935,      270600
	,	     214776,      170467,      135300,      107388
	,	      85234,       67650,       53694,       42617
	,	      33825,       26847,       21308,       16913
	,	      13423,       10654,        8456,        6712
	,	       5327,        4228,        3356,        2664
	,	       2114,        1678,        1332,        1057
	,	        839,         666,         529,         419
	,	        333,         264,         210,         166
	,	        132,         105,          83,           0
	}
,	{
		   68174084,    54109806,    42946982,    34087042
	,	   27054903,    21473491,    17043521,    13527452
	,	   10736745,     8521761,     6763726,     5368373
	,	    4260880,     3381863,     2684186,     2130440
	,	    1690931,     1342093,     1065220,      845466
	,	     671047,      532610,      422733,      335523
	,	     266305,      211366,      167762,      133153
	,	     105683,       83881,       66576,       52842
	,	      41940,       33288,       26421,       20970
	,	      16644,       13210,       10485,        8322
	,	       6605,        5243,        4161,        3303
	,	       2621,        2081,        1651,        1311
	,	       1040,         826,         655,         520
	,	        413,         328,         260,         206
	,	        164,         130,         103,          82
	,	         65,          52,          41,           0
	}
,	{
		   33818640,    26841872,    21304408,    16909320
	,	   13420936,    10652204,     8454660,     6710468
	,	    5326102,     4227330,     3355234,     2663051
	,	    2113665,     1677617,     1331526,     1056833
	,	     838809,      665763,      528416,      419404
	,	     332881,      264208,      209702,      166441
	,	     132104,      104851,       83220,       66052
	,	      52426,       41610,       33026,       26213
	,	      20805,       16513,       13106,       10403
	,	       8257,        6553,        5201,        4128
	,	       3277,        2601,        2064,        1638
	,	       1300,        1032,         819,         650
	,	        516,         410,         325,         258
	,	        205,         163,         129,         102
	,	         81,          65,          51,          41
	,	         32,          26,          20,           0
	}
,	{
		   16843009,    13368305,    10610431,     8421505
	,	    6684153,     5305215,     4210752,     3342076
	,	    2652608,     2105376,     1671038,     1326304
	,	    1052688,      835519,      663152,      526344
	,	     417760,      331576,      263172,      208880
	,	     165788,      131586,      104440,       82894
	,	      65793,       52220,       41447,       32897
	,	      26110,       20723,       16448,       13055
	,	      10362,        8224,        6527,        5181
	,	       4112,        3264,        2590,        2056
	,	       1632,        1295,        1028,         816
	,	        648,         514,         408,         324
	,	        257,         204,         162,         129
	,	        102,          81,          64,          51
	,	         40,          32,          25,          20
	,	         16,          13,          10,           0
	}
,	{
		    8405024,     6671072,     5294833,     4202512
	,	    3335536,     2647417,     2101256,     1667768
	,	    1323708,     1050628,      833884,      661854
	,	     525314,      416942,      330927,      262657
	,	     208471,      165464,      131329,      104236
	,	      82732,       65664,       52118,       41366
	,	      32832,       26059,       20683,       16416
	,	      13029,       10341,        8208,        6515
	,	       5171,        4104,        3257,        2585
	,	       2052,        1629,        1293,        1026
	,	        814,         646,         513,         407
	,	        323,         257,         204,         162
	,	        128,         102,          81,          64
	,	         51,          40,          32,          25
	,	         20,          16,          13,          10
	,	          8,           6,           5,           0
	}
,	{
		    4198404,     3332275,     2644829,     2099202
	,	    1666138,     1322414,     1049601,      833069
	,	     661207,      524801,      416534,      330604
	,	     262400,      208267,      165302,      131200
	,	     104134,       82651,       65600,       52067
	,	      41325,       32800,       26033,       20663
	,	      16400,       13017,       10331,        8200
	,	       6508,        5166,        4100,        3254
	,	       2583,        2050,        1627,        1291
	,	       1025,         814,         646,         513
	,	        407,         323,         256,         203
	,	        161,         128,         102,          81
	,	         64,          51,          40,          32
	,	         25,          20,          16,          13
	,	         10,           8,           6,           5
	,	          4,           3,           3,           0
	}
,	{
		    2098177,     1665324,     1321768,     1049088
	,	     832662,      660884,      524544,      416331
	,	     330442,      262272,      208165,      165221
	,	     131136,      104083,       82611,       65568
	,	      52041,       41305,       32784,       26021
	,	      20653,       16392,       13010,       10326
	,	       8196,        6505,        5163,        4098
	,	       3253,        2582,        2049,        1626
	,	       1291,        1025,         813,         645
	,	        512,         407,         323,         256
	,	        203,         161,         128,         102
	,	         81,          64,          51,          40
	,	         32,          25,          20,          16
	,	         13,          10,           8,           6
	,	          5,           4,           3,           3
	,	          2,           2,           1,           0
	}
,	{
		    1048832,      832459,      660723,      524416
	,	     416229,      330361,      262208,      208115
	,	     165181,      131104,      104057,       82590
	,	      65552,       52029,       41295,       32776
	,	      26014,       20648,       16388,       13007
	,	      10324,        8194,        6504,        5162
	,	       4097,        3252,        2581,        2049
	,	       1626,        1290,        1024,         813
	,	        645,         512,         406,         323
	,	        256,         203,         161,         128
	,	        102,          81,          64,          51
	,	         40,          32,          25,          20
	,	         16,          13,          10,           8
	,	          6,           5,           4,           3
	,	          3,           2,           2,           1
	,	          1,           1,           1,           0
	}
,	{
		     524352,      416178,      330321,      262176
	,	     208089,      165161,      131088,      104045
	,	      82580,       65544,       52022,       41290
	,	      32772,       26011,       20645,       16386
	,	      13006,       10323,        8193,        6503
	,	       5161,        4097,        3251,        2581
	,	       2048,        1626,        1290,        1024
	,	        813,         645,         512,         406
	,	        323,         256,         203,         161
	,	        128,         102,          81,          64
	,	         51,          40,          32,          25
	,	         20,          16,          13,          10
	,	          8,           6,           5,           4
	,	          3,           3,           2,           2
	,	          1,           1,           1,           1
	,	          1,           0,           0,           0
	}
,	{
		     262160,      208077,      165150,      131080
	,	     104038,       82575,       65540,       52019
	,	      41288,       32770,       26010,       20644
	,	      16385,       13005,       10322,        8193
	,	       6502,        5161,        4096,        3251
	,	       2580,        2048,        1626,        1290
	,	       1024,         813,         645,         512
	,	        406,         323,         256,         203
	,	        161,         128,         102,          81
	,	         64,          51,          40,          32
	,	         25,          20,          16,          13
	,	         10,           8,           6,           5
	,	          4,           3,           3,           2
	,	          2,           1,           1,           1
	,	          1,           1,           0,           0
	,	          0,           0,           0,           0
	}
,	{
		     131076,      104035,       82573,       65538
	,	      52018,       41286,       32769,       26009
	,	      20643,       16385,       13004,       10322
	,	       8192,        6502,        5161,        4096
	,	       3251,        2580,        2048,        1626
	,	       1290,        1024,         813,         645
	,	        512,         406,         323,         256
	,	        203,         161,         128,         102
	,	         81,          64,          51,          40
	,	         32,          25,          20,          16
	,	         13,          10,           8,           6
	,	          5,           4,           3,           3
	,	          2,           2,           1,           1
	,	          1,           1,           1,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	}
,	{
		      65537,       52017,       41286,       32769
	,	      26008,       20643,       16384,       13004
	,	      10321,        8192,        6502,        5161
	,	       4096,        3251,        2580,        2048
	,	       1626,        1290,        1024,         813
	,	        645,         512,         406,         323
	,	        256,         203,         161,         128
	,	        102,          81,          64,          51
	,	         40,          32,          25,          20
	,	         16,          13,          10,           8
	,	          6,           5,           4,           3
	,	          3,           2,           2,           1
	,	          1,           1,           1,           1
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	,	          0,           0,           0,           0
	}
,	{
		-1717986918, -1363567121, -1082263941,  -858993459
	,	 -681783560,  -541131970,  -429496730,  -340891780
	,	 -270565985,  -214748365,  -170445890,  -135282993
	,	 -107374182,   -85222945,   -67641496,   -53687091
	,	  -42611473,   -33820748,   -26843546,   -21305736
	,	  -16910374,   -13421773,   -10652868,    -8455187
	,	   -6710886,    -5326434,    -4227594,    -3355443
	,	   -2663217,    -2113797,    -1677722,    -1331609
	,	   -1056898,     -838861,     -665804,     -528449
	,	    -419430,     -332902,     -264225,     -209715
	,	    -166451,     -132112,     -104858,      -83226
	,	     -66056,      -52429,      -41613,      -33028
	,	     -26214,      -20806,      -16514,      -13107
	,	     -10403,       -8257,       -6554,       -5202
	,	      -4129,       -3277,       -2601,       -2064
	,	      -1638,       -1300,       -1032,           0
	}
,	{
		 -858993459,  -681783560,  -541131970,  -429496730
	,	 -340891780,  -270565985,  -214748365,  -170445890
	,	 -135282993,  -107374182,   -85222945,   -67641496
	,	  -53687091,   -42611473,   -33820748,   -26843546
	,	  -21305736,   -16910374,   -13421773,   -10652868
	,	   -8455187,    -6710886,    -5326434,    -4227594
	,	   -3355443,    -2663217,    -2113797,    -1677722
	,	   -1331609,    -1056898,     -838861,     -665804
	,	    -528449,     -419430,     -332902,     -264225
	,	    -209715,     -166451,     -132112,     -104858
	,	     -83226,      -66056,      -52429,      -41613
	,	     -33028,      -26214,      -20806,      -16514
	,	     -13107,      -10403,       -8257,       -6554
	,	      -5202,       -4129,       -3277,       -2601
	,	      -2064,       -1638,       -1300,       -1032
	,	       -819,        -650,        -516,           0
	}
,	{
		  858993459,   681783560,   541131970,   429496730
	,	  340891780,   270565985,   214748365,   170445890
	,	  135282993,   107374182,    85222945,    67641496
	,	   53687091,    42611473,    33820748,    26843546
	,	   21305736,    16910374,    13421773,    10652868
	,	    8455187,     6710886,     5326434,     4227594
	,	    3355443,     2663217,     2113797,     1677722
	,	    1331609,     1056898,      838861,      665804
	,	     528449,      419430,      332902,      264225
	,	     209715,      166451,      132112,      104858
	,	      83226,       66056,       52429,       41613
	,	      33028,       26214,       20806,       16514
	,	      13107,       10403,        8257,        6554
	,	       5202,        4129,        3277,        2601
	,	       2064,        1638,        1300,        1032
	,	        819,         650,         516,           0
	}
,	{
		 1717986918,  1363567121,  1082263941,   858993459
	,	  681783560,   541131970,   429496730,   340891780
	,	  270565985,   214748365,   170445890,   135282993
	,	  107374182,    85222945,    67641496,    53687091
	,	   42611473,    33820748,    26843546,    21305736
	,	   16910374,    13421773,    10652868,     8455187
	,	    6710886,     5326434,     4227594,     3355443
	,	    2663217,     2113797,     1677722,     1331609
	,	    1056898,      838861,      665804,      528449
	,	     419430,      332902,      264225,      209715
	,	     166451,      132112,      104858,       83226
	,	      66056,       52429,       41613,       33028
	,	      26214,       20806,       16514,       13107
	,	      10403,        8257,        6554,        5202
	,	       4129,        3277,        2601,        2064
	,	       1638,        1300,        1032,           0
	}
,	{
		-1908874354, -1515074579, -1202515490,  -954437177
	,	 -757537289,  -601257745,  -477218588,  -378768645
	,	 -300628872,  -238609294,  -189384322,  -150314436
	,	 -119304647,   -94692161,   -75157218,   -59652324
	,	  -47346081,   -37578609,   -29826162,   -23673040
	,	  -18789305,   -14913081,   -11836520,    -9394652
	,	   -7456540,    -5918260,    -4697326,    -3728270
	,	   -2959130,    -2348663,    -1864135,    -1479565
	,	   -1174332,     -932068,     -739783,     -587166
	,	    -466034,     -369891,     -293583,     -233017
	,	    -184946,     -146791,     -116508,      -92473
	,	     -73396,      -58254,      -46236,      -36698
	,	     -29127,      -23118,      -18349,      -14564
	,	     -11559,       -9174,       -7282,       -5780
	,	      -4587,       -3641,       -2890,       -2294
	,	      -1820,       -1445,       -1147,           0
	}
,	{
		 -954437177,  -757537289,  -601257745,  -477218588
	,	 -378768645,  -300628872,  -238609294,  -189384322
	,	 -150314436,  -119304647,   -94692161,   -75157218
	,	  -59652324,   -47346081,   -37578609,   -29826162
	,	  -23673040,   -18789305,   -14913081,   -11836520
	,	   -9394652,    -7456540,    -5918260,    -4697326
	,	   -3728270,    -2959130,    -2348663,    -1864135
	,	   -1479565,    -1174332,     -932068,     -739783
	,	    -587166,     -466034,     -369891,     -293583
	,	    -233017,     -184946,     -146791,     -116508
	,	     -92473,      -73396,      -58254,      -46236
	,	     -36698,      -29127,      -23118,      -18349
	,	     -14564,      -11559,       -9174,       -7282
	,	      -5780,       -4587,       -3641,       -2890
	,	      -2294,       -1820,       -1445,       -1147
	,	       -910,        -722,        -573,           0
	}
,	{
		 -477218588,  -378768645,  -300628872,  -238609294
	,	 -189384322,  -150314436,  -119304647,   -94692161
	,	  -75157218,   -59652324,   -47346081,   -37578609
	,	  -29826162,   -23673040,   -18789305,   -14913081
	,	  -11836520,    -9394652,    -7456540,    -5918260
	,	   -4697326,    -3728270,    -2959130,    -2348663
	,	   -1864135,    -1479565,    -1174332,     -932068
	,	    -739783,     -587166,     -466034,     -369891
	,	    -293583,     -233017,     -184946,     -146791
	,	    -116508,      -92473,      -73396,      -58254
	,	     -46236,      -36698,      -29127,      -23118
	,	     -18349,      -14564,      -11559,       -9174
	,	      -7282,       -5780,       -4587,       -3641
	,	      -2890,       -2294,       -1820,       -1445
	,	      -1147,        -910,        -722,        -573
	,	       -455,        -361,        -287,           0
	}
,	{
		  477218588,   378768645,   300628872,   238609294
	,	  189384322,   150314436,   119304647,    94692161
	,	   75157218,    59652324,    47346081,    37578609
	,	   29826162,    23673040,    18789305,    14913081
	,	   11836520,     9394652,     7456540,     5918260
	,	    4697326,     3728270,     2959130,     2348663
	,	    1864135,     1479565,     1174332,      932068
	,	     739783,      587166,      466034,      369891
	,	     293583,      233017,      184946,      146791
	,	     116508,       92473,       73396,       58254
	,	      46236,       36698,       29127,       23118
	,	      18349,       14564,       11559,        9174
	,	       7282,        5780,        4587,        3641
	,	       2890,        2294,        1820,        1445
	,	       1147,         910,         722,         573
	,	        455,         361,         287,           0
	}
,	{
		  954437177,   757537289,   601257745,   477218588
	,	  378768645,   300628872,   238609294,   189384322
	,	  150314436,   119304647,    94692161,    75157218
	,	   59652324,    47346081,    37578609,    29826162
	,	   23673040,    18789305,    14913081,    11836520
	,	    9394652,     7456540,     5918260,     4697326
	,	    3728270,     2959130,     2348663,     1864135
	,	    1479565,     1174332,      932068,      739783
	,	     587166,      466034,      369891,      293583
	,	     233017,      184946,      146791,      116508
	,	      92473,       73396,       58254,       46236
	,	      36698,       29127,       23118,       18349
	,	      14564,       11559,        9174,        7282
	,	       5780,        4587,        3641,        2890
	,	       2294,        1820,        1445,        1147
	,	        910,         722,         573,           0
	}
,	{
		 1908874354,  1515074579,  1202515490,   954437177
	,	  757537289,   601257745,   477218588,   378768645
	,	  300628872,   238609294,   189384322,   150314436
	,	  119304647,    94692161,    75157218,    59652324
	,	   47346081,    37578609,    29826162,    23673040
	,	   18789305,    14913081,    11836520,     9394652
	,	    7456540,     5918260,     4697326,     3728270
	,	    2959130,     2348663,     1864135,     1479565
	,	    1174332,      932068,      739783,      587166
	,	     466034,      369891,      293583,      233017
	,	     184946,      146791,      116508,       92473
	,	      73396,       58254,       46236,       36698
	,	      29127,       23118,       18349,       14564
	,	      11559,        9174,        7282,        5780
	,	       4587,        3641,        2890,        2294
	,	       1820,        1445,        1147,           0
	}
};

#endif

static const unsigned char grp_3tab[96] = 
{
	  1,   1,   1,   0,   1,   1,   2,   1,   1,   1,   0,   1,   0,   0,   1,   2,   0,   1
,	  1,   2,   1,   0,   2,   1,   2,   2,   1,   1,   1,   0,   0,   1,   0,   2,   1,   0
,	  1,   0,   0,   0,   0,   0,   2,   0,   0,   1,   2,   0,   0,   2,   0,   2,   2,   0
,	  1,   1,   2,   0,   1,   2,   2,   1,   2,   1,   0,   2,   0,   0,   2,   2,   0,   2
,	  1,   2,   2,   0,   2,   2,   2,   2,   2,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0
};

static const unsigned char grp_5tab[384] = 
{
	 17,  17,  17,  18,  17,  17,   0,  17,  17,  19,  17,  17,  20,  17,  17,  17,  18,  17
,	 18,  18,  17,   0,  18,  17,  19,  18,  17,  20,  18,  17,  17,   0,  17,  18,   0,  17
,	  0,   0,  17,  19,   0,  17,  20,   0,  17,  17,  19,  17,  18,  19,  17,   0,  19,  17
,	 19,  19,  17,  20,  19,  17,  17,  20,  17,  18,  20,  17,   0,  20,  17,  19,  20,  17
,	 20,  20,  17,  17,  17,  18,  18,  17,  18,   0,  17,  18,  19,  17,  18,  20,  17,  18
,	 17,  18,  18,  18,  18,  18,   0,  18,  18,  19,  18,  18,  20,  18,  18,  17,   0,  18
,	 18,   0,  18,   0,   0,  18,  19,   0,  18,  20,   0,  18,  17,  19,  18,  18,  19,  18
,	  0,  19,  18,  19,  19,  18,  20,  19,  18,  17,  20,  18,  18,  20,  18,   0,  20,  18
,	 19,  20,  18,  20,  20,  18,  17,  17,   0,  18,  17,   0,   0,  17,   0,  19,  17,   0
,	 20,  17,   0,  17,  18,   0,  18,  18,   0,   0,  18,   0,  19,  18,   0,  20,  18,   0
,	 17,   0,   0,  18,   0,   0,   0,   0,   0,  19,   0,   0,  20,   0,   0,  17,  19,   0
,	 18,  19,   0,   0,  19,   0,  19,  19,   0,  20,  19,   0,  17,  20,   0,  18,  20,   0
,	  0,  20,   0,  19,  20,   0,  20,  20,   0,  17,  17,  19,  18,  17,  19,   0,  17,  19
,	 19,  17,  19,  20,  17,  19,  17,  18,  19,  18,  18,  19,   0,  18,  19,  19,  18,  19
,	 20,  18,  19,  17,   0,  19,  18,   0,  19,   0,   0,  19,  19,   0,  19,  20,   0,  19
,	 17,  19,  19,  18,  19,  19,   0,  19,  19,  19,  19,  19,  20,  19,  19,  17,  20,  19
,	 18,  20,  19,   0,  20,  19,  19,  20,  19,  20,  20,  19,  17,  17,  20,  18,  17,  20
,	  0,  17,  20,  19,  17,  20,  20,  17,  20,  17,  18,  20,  18,  18,  20,   0,  18,  20
,	 19,  18,  20,  20,  18,  20,  17,   0,  20,  18,   0,  20,   0,   0,  20,  19,   0,  20
,	 20,   0,  20,  17,  19,  20,  18,  19,  20,   0,  19,  20,  19,  19,  20,  20,  19,  20
,	 17,  20,  20,  18,  20,  20,   0,  20,  20,  19,  20,  20,  20,  20,  20,   0,   0,   0
,	  0,   0,   0,   0,   0,   0
};

static const unsigned char grp_9tab[3072] = 
{
	 21,  21,  21,   1,  21,  21,  22,  21,  21,  23,  21,  21,   0,  21,  21,  24,  21,  21
,	 25,  21,  21,   2,  21,  21,  26,  21,  21,  21,   1,  21,   1,   1,  21,  22,   1,  21
,	 23,   1,  21,   0,   1,  21,  24,   1,  21,  25,   1,  21,   2,   1,  21,  26,   1,  21
,	 21,  22,  21,   1,  22,  21,  22,  22,  21,  23,  22,  21,   0,  22,  21,  24,  22,  21
,	 25,  22,  21,   2,  22,  21,  26,  22,  21,  21,  23,  21,   1,  23,  21,  22,  23,  21
,	 23,  23,  21,   0,  23,  21,  24,  23,  21,  25,  23,  21,   2,  23,  21,  26,  23,  21
,	 21,   0,  21,   1,   0,  21,  22,   0,  21,  23,   0,  21,   0,   0,  21,  24,   0,  21
,	 25,   0,  21,   2,   0,  21,  26,   0,  21,  21,  24,  21,   1,  24,  21,  22,  24,  21
,	 23,  24,  21,   0,  24,  21,  24,  24,  21,  25,  24,  21,   2,  24,  21,  26,  24,  21
,	 21,  25,  21,   1,  25,  21,  22,  25,  21,  23,  25,  21,   0,  25,  21,  24,  25,  21
,	 25,  25,  21,   2,  25,  21,  26,  25,  21,  21,   2,  21,   1,   2,  21,  22,   2,  21
,	 23,   2,  21,   0,   2,  21,  24,   2,  21,  25,   2,  21,   2,   2,  21,  26,   2,  21
,	 21,  26,  21,   1,  26,  21,  22,  26,  21,  23,  26,  21,   0,  26,  21,  24,  26,  21
,	 25,  26,  21,   2,  26,  21,  26,  26,  21,  21,  21,   1,   1,  21,   1,  22,  21,   1
,	 23,  21,   1,   0,  21,   1,  24,  21,   1,  25,  21,   1,   2,  21,   1,  26,  21,   1
,	 21,   1,   1,   1,   1,   1,  22,   1,   1,  23,   1,   1,   0,   1,   1,  24,   1,   1
,	 25,   1,   1,   2,   1,   1,  26,   1,   1,  21,  22,   1,   1,  22,   1,  22,  22,   1
,	 23,  22,   1,   0,  22,   1,  24,  22,   1,  25,  22,   1,   2,  22,   1,  26,  22,   1
,	 21,  23,   1,   1,  23,   1,  22,  23,   1,  23,  23,   1,   0,  23,   1,  24,  23,   1
,	 25,  23,   1,   2,  23,   1,  26,  23,   1,  21,   0,   1,   1,   0,   1,  22,   0,   1
,	 23,   0,   1,   0,   0,   1,  24,   0,   1,  25,   0,   1,   2,   0,   1,  26,   0,   1
,	 21,  24,   1,   1,  24,   1,  22,  24,   1,  23,  24,   1,   0,  24,   1,  24,  24,   1
,	 25,  24,   1,   2,  24,   1,  26,  24,   1,  21,  25,   1,   1,  25,   1,  22,  25,   1
,	 23,  25,   1,   0,  25,   1,  24,  25,   1,  25,  25,   1,   2,  25,   1,  26,  25,   1
,	 21,   2,   1,   1,   2,   1,  22,   2,   1,  23,   2,   1,   0,   2,   1,  24,   2,   1
,	 25,   2,   1,   2,   2,   1,  26,   2,   1,  21,  26,   1,   1,  26,   1,  22,  26,   1
,	 23,  26,   1,   0,  26,   1,  24,  26,   1,  25,  26,   1,   2,  26,   1,  26,  26,   1
,	 21,  21,  22,   1,  21,  22,  22,  21,  22,  23,  21,  22,   0,  21,  22,  24,  21,  22
,	 25,  21,  22,   2,  21,  22,  26,  21,  22,  21,   1,  22,   1,   1,  22,  22,   1,  22
,	 23,   1,  22,   0,   1,  22,  24,   1,  22,  25,   1,  22,   2,   1,  22,  26,   1,  22
,	 21,  22,  22,   1,  22,  22,  22,  22,  22,  23,  22,  22,   0,  22,  22,  24,  22,  22
,	 25,  22,  22,   2,  22,  22,  26,  22,  22,  21,  23,  22,   1,  23,  22,  22,  23,  22
,	 23,  23,  22,   0,  23,  22,  24,  23,  22,  25,  23,  22,   2,  23,  22,  26,  23,  22
,	 21,   0,  22,   1,   0,  22,  22,   0,  22,  23,   0,  22,   0,   0,  22,  24,   0,  22
,	 25,   0,  22,   2,   0,  22,  26,   0,  22,  21,  24,  22,   1,  24,  22,  22,  24,  22
,	 23,  24,  22,   0,  24,  22,  24,  24,  22,  25,  24,  22,   2,  24,  22,  26,  24,  22
,	 21,  25,  22,   1,  25,  22,  22,  25,  22,  23,  25,  22,   0,  25,  22,  24,  25,  22
,	 25,  25,  22,   2,  25,  22,  26,  25,  22,  21,   2,  22,   1,   2,  22,  22,   2,  22
,	 23,   2,  22,   0,   2,  22,  24,   2,  22,  25,   2,  22,   2,   2,  22,  26,   2,  22
,	 21,  26,  22,   1,  26,  22,  22,  26,  22,  23,  26,  22,   0,  26,  22,  24,  26,  22
,	 25,  26,  22,   2,  26,  22,  26,  26,  22,  21,  21,  23,   1,  21,  23,  22,  21,  23
,	 23,  21,  23,   0,  21,  23,  24,  21,  23,  25,  21,  23,   2,  21,  23,  26,  21,  23
,	 21,   1,  23,   1,   1,  23,  22,   1,  23,  23,   1,  23,   0,   1,  23,  24,   1,  23
,	 25,   1,  23,   2,   1,  23,  26,   1,  23,  21,  22,  23,   1,  22,  23,  22,  22,  23
,	 23,  22,  23,   0,  22,  23,  24,  22,  23,  25,  22,  23,   2,  22,  23,  26,  22,  23
,	 21,  23,  23,   1,  23,  23,  22,  23,  23,  23,  23,  23,   0,  23,  23,  24,  23,  23
,	 25,  23,  23,   2,  23,  23,  26,  23,  23,  21,   0,  23,   1,   0,  23,  22,   0,  23
,	 23,   0,  23,   0,   0,  23,  24,   0,  23,  25,   0,  23,   2,   0,  23,  26,   0,  23
,	 21,  24,  23,   1,  24,  23,  22,  24,  23,  23,  24,  23,   0,  24,  23,  24,  24,  23
,	 25,  24,  23,   2,  24,  23,  26,  24,  23,  21,  25,  23,   1,  25,  23,  22,  25,  23
,	 23,  25,  23,   0,  25,  23,  24,  25,  23,  25,  25,  23,   2,  25,  23,  26,  25,  23
,	 21,   2,  23,   1,   2,  23,  22,   2,  23,  23,   2,  23,   0,   2,  23,  24,   2,  23
,	 25,   2,  23,   2,   2,  23,  26,   2,  23,  21,  26,  23,   1,  26,  23,  22,  26,  23
,	 23,  26,  23,   0,  26,  23,  24,  26,  23,  25,  26,  23,   2,  26,  23,  26,  26,  23
,	 21,  21,   0,   1,  21,   0,  22,  21,   0,  23,  21,   0,   0,  21,   0,  24,  21,   0
,	 25,  21,   0,   2,  21,   0,  26,  21,   0,  21,   1,   0,   1,   1,   0,  22,   1,   0
,	 23,   1,   0,   0,   1,   0,  24,   1,   0,  25,   1,   0,   2,   1,   0,  26,   1,   0
,	 21,  22,   0,   1,  22,   0,  22,  22,   0,  23,  22,   0,   0,  22,   0,  24,  22,   0
,	 25,  22,   0,   2,  22,   0,  26,  22,   0,  21,  23,   0,   1,  23,   0,  22,  23,   0
,	 23,  23,   0,   0,  23,   0,  24,  23,   0,  25,  23,   0,   2,  23,   0,  26,  23,   0
,	 21,   0,   0,   1,   0,   0,  22,   0,   0,  23,   0,   0,   0,   0,   0,  24,   0,   0
,	 25,   0,   0,   2,   0,   0,  26,   0,   0,  21,  24,   0,   1,  24,   0,  22,  24,   0
,	 23,  24,   0,   0,  24,   0,  24,  24,   0,  25,  24,   0,   2,  24,   0,  26,  24,   0
,	 21,  25,   0,   1,  25,   0,  22,  25,   0,  23,  25,   0,   0,  25,   0,  24,  25,   0
,	 25,  25,   0,   2,  25,   0,  26,  25,   0,  21,   2,   0,   1,   2,   0,  22,   2,   0
,	 23,   2,   0,   0,   2,   0,  24,   2,   0,  25,   2,   0,   2,   2,   0,  26,   2,   0
,	 21,  26,   0,   1,  26,   0,  22,  26,   0,  23,  26,   0,   0,  26,   0,  24,  26,   0
,	 25,  26,   0,   2,  26,   0,  26,  26,   0,  21,  21,  24,   1,  21,  24,  22,  21,  24
,	 23,  21,  24,   0,  21,  24,  24,  21,  24,  25,  21,  24,   2,  21,  24,  26,  21,  24
,	 21,   1,  24,   1,   1,  24,  22,   1,  24,  23,   1,  24,   0,   1,  24,  24,   1,  24
,	 25,   1,  24,   2,   1,  24,  26,   1,  24,  21,  22,  24,   1,  22,  24,  22,  22,  24
,	 23,  22,  24,   0,  22,  24,  24,  22,  24,  25,  22,  24,   2,  22,  24,  26,  22,  24
,	 21,  23,  24,   1,  23,  24,  22,  23,  24,  23,  23,  24,   0,  23,  24,  24,  23,  24
,	 25,  23,  24,   2,  23,  24,  26,  23,  24,  21,   0,  24,   1,   0,  24,  22,   0,  24
,	 23,   0,  24,   0,   0,  24,  24,   0,  24,  25,   0,  24,   2,   0,  24,  26,   0,  24
,	 21,  24,  24,   1,  24,  24,  22,  24,  24,  23,  24,  24,   0,  24,  24,  24,  24,  24
,	 25,  24,  24,   2,  24,  24,  26,  24,  24,  21,  25,  24,   1,  25,  24,  22,  25,  24
,	 23,  25,  24,   0,  25,  24,  24,  25,  24,  25,  25,  24,   2,  25,  24,  26,  25,  24
,	 21,   2,  24,   1,   2,  24,  22,   2,  24,  23,   2,  24,   0,   2,  24,  24,   2,  24
,	 25,   2,  24,   2,   2,  24,  26,   2,  24,  21,  26,  24,   1,  26,  24,  22,  26,  24
,	 23,  26,  24,   0,  26,  24,  24,  26,  24,  25,  26,  24,   2,  26,  24,  26,  26,  24
,	 21,  21,  25,   1,  21,  25,  22,  21,  25,  23,  21,  25,   0,  21,  25,  24,  21,  25
,	 25,  21,  25,   2,  21,  25,  26,  21,  25,  21,   1,  25,   1,   1,  25,  22,   1,  25
,	 23,   1,  25,   0,   1,  25,  24,   1,  25,  25,   1,  25,   2,   1,  25,  26,   1,  25
,	 21,  22,  25,   1,  22,  25,  22,  22,  25,  23,  22,  25,   0,  22,  25,  24,  22,  25
,	 25,  22,  25,   2,  22,  25,  26,  22,  25,  21,  23,  25,   1,  23,  25,  22,  23,  25
,	 23,  23,  25,   0,  23,  25,  24,  23,  25,  25,  23,  25,   2,  23,  25,  26,  23,  25
,	 21,   0,  25,   1,   0,  25,  22,   0,  25,  23,   0,  25,   0,   0,  25,  24,   0,  25
,	 25,   0,  25,   2,   0,  25,  26,   0,  25,  21,  24,  25,   1,  24,  25,  22,  24,  25
,	 23,  24,  25,   0,  24,  25,  24,  24,  25,  25,  24,  25,   2,  24,  25,  26,  24,  25
,	 21,  25,  25,   1,  25,  25,  22,  25,  25,  23,  25,  25,   0,  25,  25,  24,  25,  25
,	 25,  25,  25,   2,  25,  25,  26,  25,  25,  21,   2,  25,   1,   2,  25,  22,   2,  25
,	 23,   2,  25,   0,   2,  25,  24,   2,  25,  25,   2,  25,   2,   2,  25,  26,   2,  25
,	 21,  26,  25,   1,  26,  25,  22,  26,  25,  23,  26,  25,   0,  26,  25,  24,  26,  25
,	 25,  26,  25,   2,  26,  25,  26,  26,  25,  21,  21,   2,   1,  21,   2,  22,  21,   2
,	 23,  21,   2,   0,  21,   2,  24,  21,   2,  25,  21,   2,   2,  21,   2,  26,  21,   2
,	 21,   1,   2,   1,   1,   2,  22,   1,   2,  23,   1,   2,   0,   1,   2,  24,   1,   2
,	 25,   1,   2,   2,   1,   2,  26,   1,   2,  21,  22,   2,   1,  22,   2,  22,  22,   2
,	 23,  22,   2,   0,  22,   2,  24,  22,   2,  25,  22,   2,   2,  22,   2,  26,  22,   2
,	 21,  23,   2,   1,  23,   2,  22,  23,   2,  23,  23,   2,   0,  23,   2,  24,  23,   2
,	 25,  23,   2,   2,  23,   2,  26,  23,   2,  21,   0,   2,   1,   0,   2,  22,   0,   2
,	 23,   0,   2,   0,   0,   2,  24,   0,   2,  25,   0,   2,   2,   0,   2,  26,   0,   2
,	 21,  24,   2,   1,  24,   2,  22,  24,   2,  23,  24,   2,   0,  24,   2,  24,  24,   2
,	 25,  24,   2,   2,  24,   2,  26,  24,   2,  21,  25,   2,   1,  25,   2,  22,  25,   2
,	 23,  25,   2,   0,  25,   2,  24,  25,   2,  25,  25,   2,   2,  25,   2,  26,  25,   2
,	 21,   2,   2,   1,   2,   2,  22,   2,   2,  23,   2,   2,   0,   2,   2,  24,   2,   2
,	 25,   2,   2,   2,   2,   2,  26,   2,   2,  21,  26,   2,   1,  26,   2,  22,  26,   2
,	 23,  26,   2,   0,  26,   2,  24,  26,   2,  25,  26,   2,   2,  26,   2,  26,  26,   2
,	 21,  21,  26,   1,  21,  26,  22,  21,  26,  23,  21,  26,   0,  21,  26,  24,  21,  26
,	 25,  21,  26,   2,  21,  26,  26,  21,  26,  21,   1,  26,   1,   1,  26,  22,   1,  26
,	 23,   1,  26,   0,   1,  26,  24,   1,  26,  25,   1,  26,   2,   1,  26,  26,   1,  26
,	 21,  22,  26,   1,  22,  26,  22,  22,  26,  23,  22,  26,   0,  22,  26,  24,  22,  26
,	 25,  22,  26,   2,  22,  26,  26,  22,  26,  21,  23,  26,   1,  23,  26,  22,  23,  26
,	 23,  23,  26,   0,  23,  26,  24,  23,  26,  25,  23,  26,   2,  23,  26,  26,  23,  26
,	 21,   0,  26,   1,   0,  26,  22,   0,  26,  23,   0,  26,   0,   0,  26,  24,   0,  26
,	 25,   0,  26,   2,   0,  26,  26,   0,  26,  21,  24,  26,   1,  24,  26,  22,  24,  26
,	 23,  24,  26,   0,  24,  26,  24,  24,  26,  25,  24,  26,   2,  24,  26,  26,  24,  26
,	 21,  25,  26,   1,  25,  26,  22,  25,  26,  23,  25,  26,   0,  25,  26,  24,  25,  26
,	 25,  25,  26,   2,  25,  26,  26,  25,  26,  21,   2,  26,   1,   2,  26,  22,   2,  26
,	 23,   2,  26,   0,   2,  26,  24,   2,  26,  25,   2,  26,   2,   2,  26,  26,   2,  26
,	 21,  26,  26,   1,  26,  26,  22,  26,  26,  23,  26,  26,   0,  26,  26,  24,  26,  26
,	 25,  26,  26,   2,  26,  26,  26,  26,  26,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
,	  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
};

#endif

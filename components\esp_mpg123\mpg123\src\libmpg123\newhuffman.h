/*
	nwehuffman.h: optimized huffman tables (radix-4 lookup)

	copyright 1995-2013 by the mpg123 project - free software under the terms of the LGPL 2.1
	see COPYING and AUTHORS files in distribution or http://mpg123.org
	initially written by <PERSON><PERSON><PERSON>
*/


#ifndef _MPG123_NEWHUFFMAN_H_
#define _MPG123_NEWHUFFMAN_H_

struct newhuff 
{
  unsigned int linbits;
  const short *table;
};

static const short tab0_[] =
{
	     0,      0,      0,      0,      0,      0,      0,      0,
	     0,      0,      0,      0,      0,      0,      0,      0,
};

static const short tab1_[] =
{
	0x0311, 0x0311, 0x0301, 0x0301, 0x0210, 0x0210, 0x0210, 0x0210,
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100,
};

static const short tab2_[] =
{
	   -16,    -32, 0x0311, 0x0311, 0x0301, 0x0301, 0x0310, 0x0310, 
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 
	0x0222, 0x0222, 0x0222, 0x0222, 0x0202, 0x0202, 0x0202, 0x0202, 
	0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 
	0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 
	0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120,
};

static const short tab3_[] =
{
	   -16,    -32, 0x0310, 0x0310, 0x0211, 0x0211, 0x0211, 0x0211, 
	0x0201, 0x0201, 0x0201, 0x0201, 0x0200, 0x0200, 0x0200, 0x0200, 
	0x0222, 0x0222, 0x0222, 0x0222, 0x0202, 0x0202, 0x0202, 0x0202, 
	0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 
	0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 
	0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120
};

static const short tab5_[] =
{
	   -16,    -32, 0x0311, 0x0311, 0x0301, 0x0301, 0x0310, 0x0310, 
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 
	0x0433, 0x0423, 0x0332, 0x0332, 0x0231, 0x0231, 0x0231, 0x0231, 
	0x0313, 0x0313, 0x0303, 0x0303, 0x0330, 0x0330, 0x0322, 0x0322, 
	0x0212, 0x0212, 0x0212, 0x0212, 0x0221, 0x0221, 0x0221, 0x0221, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220,
};

static const short tab6_[] =
{
	   -16,    -32,    -48, 0x0412, 0x0421, 0x0420, 0x0301, 0x0301, 
	0x0211, 0x0211, 0x0211, 0x0211, 0x0310, 0x0310, 0x0300, 0x0300, 
	0x0333, 0x0333, 0x0303, 0x0303, 0x0223, 0x0223, 0x0223, 0x0223, 
	0x0232, 0x0232, 0x0232, 0x0232, 0x0230, 0x0230, 0x0230, 0x0230, 
	0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 
	0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 
	0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 
	0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102,
};

static const short tab7_[] =
{
	   -16,    -32,    -48, 0x0411, 0x0301, 0x0301, 0x0310, 0x0310, 
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 
	   -48,    -64,    -80, 0x0415, 0x0451,    -96, 0x0450,   -112, 
	0x0424, 0x0442, 0x0314, 0x0314, 0x0341, 0x0341, 0x0340, 0x0340, 
	0x0404, 0x0423, 0x0432, 0x0403, 0x0313, 0x0313, 0x0331, 0x0331, 
	0x0330, 0x0330, 0x0322, 0x0322, 0x0212, 0x0212, 0x0212, 0x0212, 
	0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220, 
	0x0255, 0x0255, 0x0255, 0x0255, 0x0245, 0x0245, 0x0245, 0x0245, 
	0x0254, 0x0254, 0x0254, 0x0254, 0x0253, 0x0253, 0x0253, 0x0253, 
	0x0135, 0x0135, 0x0135, 0x0135, 0x0135, 0x0135, 0x0135, 0x0135, 
	0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 
	0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 
	0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 
	0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 
	0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133,
};

static const short tab8_[] =
{
	   -16,    -32, 0x0412, 0x0421, 0x0211, 0x0211, 0x0211, 0x0211, 
	0x0301, 0x0301, 0x0310, 0x0310, 0x0200, 0x0200, 0x0200, 0x0200, 
	   -32,    -48,    -64, 0x0415, 0x0451,    -80,    -96, 0x0424, 
	0x0442, 0x0414, 0x0341, 0x0341, 0x0404, 0x0440, 0x0423, 0x0432, 
	0x0413, 0x0431, 0x0403, 0x0430, 0x0222, 0x0222, 0x0222, 0x0222, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220, 
	0x0355, 0x0355, 0x0354, 0x0354, 0x0245, 0x0245, 0x0245, 0x0245, 
	0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 
	0x0235, 0x0235, 0x0235, 0x0235, 0x0244, 0x0244, 0x0244, 0x0244, 
	0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 
	0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 
	0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 
	0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 
	0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133,
};

static const short tab9_[] =
{
	   -16,    -32,    -48,    -64,    -80, 0x0412, 0x0421, 0x0420, 
	0x0311, 0x0311, 0x0301, 0x0301, 0x0310, 0x0310, 0x0300, 0x0300, 
	   -80, 0x0435, 0x0453,    -96, 0x0444, 0x0425, 0x0452, 0x0415, 
	0x0351, 0x0351, 0x0334, 0x0334, 0x0343, 0x0343, 0x0450, 0x0404, 
	0x0324, 0x0324, 0x0342, 0x0342, 0x0333, 0x0333, 0x0340, 0x0340, 
	0x0214, 0x0214, 0x0214, 0x0214, 0x0241, 0x0241, 0x0241, 0x0241, 
	0x0223, 0x0223, 0x0223, 0x0223, 0x0232, 0x0232, 0x0232, 0x0232, 
	0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 
	0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 
	0x0203, 0x0203, 0x0203, 0x0203, 0x0230, 0x0230, 0x0230, 0x0230, 
	0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 
	0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 
	0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 
	0x0145, 0x0145, 0x0145, 0x0145, 0x0145, 0x0145, 0x0145, 0x0145, 
	0x0154, 0x0154, 0x0154, 0x0154, 0x0154, 0x0154, 0x0154, 0x0154, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105,
};

static const short tab10_[] =
{
	   -16,    -32,    -48, 0x0411, 0x0301, 0x0301, 0x0310, 0x0310, 
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 
	   -48,    -64,    -80,    -96,   -112,   -128,   -144, 0x0417, 
	0x0471,   -160,   -176,   -192, 0x0416, 0x0461, 0x0460,   -208, 
	  -208,   -224, 0x0414, 0x0441, 0x0440, 0x0423, 0x0432, 0x0403, 
	0x0313, 0x0313, 0x0331, 0x0331, 0x0330, 0x0330, 0x0322, 0x0322, 
	0x0212, 0x0212, 0x0212, 0x0212, 0x0221, 0x0221, 0x0221, 0x0221, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220, 
	0x0377, 0x0377, 0x0367, 0x0367, 0x0376, 0x0376, 0x0357, 0x0357, 
	0x0375, 0x0375, 0x0366, 0x0366, 0x0247, 0x0247, 0x0247, 0x0247, 
	0x0274, 0x0274, 0x0274, 0x0274, 0x0256, 0x0256, 0x0256, 0x0256, 
	0x0265, 0x0265, 0x0265, 0x0265, 0x0237, 0x0237, 0x0237, 0x0237, 
	0x0273, 0x0273, 0x0273, 0x0273, 0x0246, 0x0246, 0x0246, 0x0246, 
	0x0355, 0x0355, 0x0354, 0x0354, 0x0263, 0x0263, 0x0263, 0x0263, 
	0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 
	0x0172, 0x0172, 0x0172, 0x0172, 0x0172, 0x0172, 0x0172, 0x0172, 
	0x0264, 0x0264, 0x0264, 0x0264, 0x0207, 0x0207, 0x0207, 0x0207, 
	0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 
	0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 
	0x0245, 0x0245, 0x0245, 0x0245, 0x0235, 0x0235, 0x0235, 0x0235, 
	0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 
	0x0253, 0x0253, 0x0253, 0x0253, 0x0244, 0x0244, 0x0244, 0x0244, 
	0x0136, 0x0136, 0x0136, 0x0136, 0x0136, 0x0136, 0x0136, 0x0136, 
	0x0126, 0x0126, 0x0126, 0x0126, 0x0126, 0x0126, 0x0126, 0x0126, 
	0x0225, 0x0225, 0x0225, 0x0225, 0x0252, 0x0252, 0x0252, 0x0252, 
	0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 
	0x0151, 0x0151, 0x0151, 0x0151, 0x0151, 0x0151, 0x0151, 0x0151, 
	0x0234, 0x0234, 0x0234, 0x0234, 0x0243, 0x0243, 0x0243, 0x0243, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 
	0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 
	0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 
	0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 
	0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 0x0104,
};

static const short tab11_[] =
{
	   -16,    -32,    -48,    -64, 0x0412,    -80, 0x0311, 0x0311, 
	0x0301, 0x0301, 0x0310, 0x0310, 0x0200, 0x0200, 0x0200, 0x0200, 
	   -80,    -96,   -112,   -128,   -144, 0x0427, 0x0472,   -160, 
	0x0371, 0x0371, 0x0417, 0x0470, 0x0436, 0x0463, 0x0460,   -176, 
	  -176, 0x0415, 0x0362, 0x0362, 0x0426, 0x0406, 0x0316, 0x0316, 
	0x0361, 0x0361, 0x0451, 0x0434, 0x0450,   -192, 0x0424, 0x0442, 
	0x0414, 0x0441, 0x0404, 0x0440, 0x0323, 0x0323, 0x0332, 0x0332, 
	0x0213, 0x0213, 0x0213, 0x0213, 0x0231, 0x0231, 0x0231, 0x0231, 
	0x0303, 0x0303, 0x0330, 0x0330, 0x0222, 0x0222, 0x0222, 0x0222, 
	0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 
	0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 
	0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 
	0x0277, 0x0277, 0x0277, 0x0277, 0x0267, 0x0267, 0x0267, 0x0267, 
	0x0276, 0x0276, 0x0276, 0x0276, 0x0275, 0x0275, 0x0275, 0x0275, 
	0x0266, 0x0266, 0x0266, 0x0266, 0x0247, 0x0247, 0x0247, 0x0247, 
	0x0274, 0x0274, 0x0274, 0x0274, 0x0357, 0x0357, 0x0355, 0x0355, 
	0x0256, 0x0256, 0x0256, 0x0256, 0x0265, 0x0265, 0x0265, 0x0265, 
	0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 
	0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 
	0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 
	0x0245, 0x0245, 0x0245, 0x0245, 0x0254, 0x0254, 0x0254, 0x0254, 
	0x0235, 0x0235, 0x0235, 0x0235, 0x0253, 0x0253, 0x0253, 0x0253, 
	0x0164, 0x0164, 0x0164, 0x0164, 0x0164, 0x0164, 0x0164, 0x0164, 
	0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 
	0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 0x0144, 
	0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 
	0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 
	0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133,
};

static const short tab12_[] =
{
	   -16,    -32,    -48,    -64,    -80,    -96, 0x0412, 0x0421, 
	  -112, 0x0400, 0x0311, 0x0311, 0x0301, 0x0301, 0x0310, 0x0310, 
	  -112,   -128,   -144,   -160, 0x0456, 0x0437,   -176, 0x0427, 
	0x0472, 0x0446, 0x0464, 0x0417, 0x0471,   -192, 0x0436, 0x0463, 
	0x0445, 0x0454, 0x0444,   -192, 0x0326, 0x0326, 0x0362, 0x0362, 
	0x0361, 0x0361, 0x0416, 0x0460, 0x0435, 0x0453, 0x0425, 0x0452, 
	0x0315, 0x0315, 0x0351, 0x0351, 0x0334, 0x0334, 0x0343, 0x0343, 
	0x0450, 0x0404, 0x0324, 0x0324, 0x0342, 0x0342, 0x0314, 0x0314, 
	0x0233, 0x0233, 0x0233, 0x0233, 0x0241, 0x0241, 0x0241, 0x0241, 
	0x0223, 0x0223, 0x0223, 0x0223, 0x0232, 0x0232, 0x0232, 0x0232, 
	0x0340, 0x0340, 0x0303, 0x0303, 0x0230, 0x0230, 0x0230, 0x0230, 
	0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 0x0113, 
	0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 0x0131, 
	0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 
	0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 
	0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 
	0x0277, 0x0277, 0x0277, 0x0277, 0x0267, 0x0267, 0x0267, 0x0267, 
	0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 
	0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 
	0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 
	0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 
	0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 
	0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 
	0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 
	0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 
	0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 0x0155, 
	0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 
	0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 
	0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105,
};

static const short tab13_[] =
{
	   -16,    -32,    -48,    -64, 0x0411, 0x0401, 0x0310, 0x0310, 
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 
	   -64,    -80,    -96,   -112,   -128,   -144,   -160,   -176, 
	  -192,   -208,   -224,   -240,   -256,   -272,   -288,   -304, 
	  -304,   -320,   -336,   -352, 0x0481,   -368,   -384,   -400, 
	  -416,   -432, 0x0415, 0x0451,   -448,   -464,   -480, 0x0414, 
	0x0341, 0x0341, 0x0404, 0x0440, 0x0423, 0x0432, 0x0313, 0x0313, 
	0x0331, 0x0331, 0x0303, 0x0303, 0x0330, 0x0330, 0x0322, 0x0322, 
	0x0212, 0x0212, 0x0212, 0x0212, 0x0221, 0x0221, 0x0221, 0x0221, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220, 
	  -448,   -464,   -480,   -496,   -512,   -528,   -544,   -560, 
	  -576,   -592,   -608,   -624,   -640,   -656, 0x041f, 0x04f1, 
	0x04f0,   -656,   -672,   -688, 0x04e2,   -704, 0x041e, 0x04e1, 
	  -720,   -736,   -752,   -768,   -784,   -800, 0x04c6, 0x043d, 
	  -800, 0x042d, 0x04d2, 0x041d, 0x04b7,   -816,   -832, 0x04c3, 
	  -848, 0x044b, 0x03d1, 0x03d1, 0x040d, 0x04d0, 0x048a, 0x04a8, 
	0x044c, 0x04c4, 0x046b, 0x04b6, 0x033c, 0x033c, 0x032c, 0x032c, 
	0x03c2, 0x03c2, 0x035b, 0x035b, 0x04b5, 0x0489, 0x031c, 0x031c, 
	0x03c1, 0x03c1, 0x0498, 0x040c, 0x03c0, 0x03c0, 0x04b4, 0x046a, 
	0x04a6, 0x0479, 0x033b, 0x033b, 0x03b3, 0x03b3, 0x0488, 0x045a, 
	0x032b, 0x032b, 0x04a5, 0x0469, 0x03a4, 0x03a4, 0x0478, 0x0487, 
	0x0394, 0x0394, 0x0477, 0x0476, 0x02b2, 0x02b2, 0x02b2, 0x02b2, 
	0x021b, 0x021b, 0x021b, 0x021b, 0x02b1, 0x02b1, 0x02b1, 0x02b1, 
	0x030b, 0x030b, 0x03b0, 0x03b0, 0x0396, 0x0396, 0x034a, 0x034a, 
	0x033a, 0x033a, 0x03a3, 0x03a3, 0x0359, 0x0359, 0x0395, 0x0395, 
	0x022a, 0x022a, 0x022a, 0x022a, 0x02a2, 0x02a2, 0x02a2, 0x02a2, 
	0x021a, 0x021a, 0x021a, 0x021a, 0x02a1, 0x02a1, 0x02a1, 0x02a1, 
	0x030a, 0x030a, 0x0368, 0x0368, 0x02a0, 0x02a0, 0x02a0, 0x02a0, 
	0x0386, 0x0386, 0x0349, 0x0349, 0x0293, 0x0293, 0x0293, 0x0293, 
	0x0339, 0x0339, 0x0358, 0x0358, 0x0385, 0x0385, 0x0367, 0x0367, 
	0x0229, 0x0229, 0x0229, 0x0229, 0x0292, 0x0292, 0x0292, 0x0292, 
	0x0357, 0x0357, 0x0375, 0x0375, 0x0238, 0x0238, 0x0238, 0x0238, 
	0x0283, 0x0283, 0x0283, 0x0283, 0x0366, 0x0366, 0x0347, 0x0347, 
	0x0374, 0x0374, 0x0356, 0x0356, 0x0365, 0x0365, 0x0373, 0x0373, 
	0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 
	0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 
	0x0209, 0x0209, 0x0209, 0x0209, 0x0290, 0x0290, 0x0290, 0x0290, 
	0x0248, 0x0248, 0x0248, 0x0248, 0x0284, 0x0284, 0x0284, 0x0284, 
	0x0272, 0x0272, 0x0272, 0x0272, 0x0346, 0x0346, 0x0364, 0x0364, 
	0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 
	0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 
	0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 
	0x0237, 0x0237, 0x0237, 0x0237, 0x0227, 0x0227, 0x0227, 0x0227, 
	0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 
	0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 
	0x0255, 0x0255, 0x0255, 0x0255, 0x0207, 0x0207, 0x0207, 0x0207, 
	0x0270, 0x0270, 0x0270, 0x0270, 0x0236, 0x0236, 0x0236, 0x0236, 
	0x0263, 0x0263, 0x0263, 0x0263, 0x0245, 0x0245, 0x0245, 0x0245, 
	0x0254, 0x0254, 0x0254, 0x0254, 0x0226, 0x0226, 0x0226, 0x0226, 
	0x0262, 0x0262, 0x0262, 0x0262, 0x0235, 0x0235, 0x0235, 0x0235, 
	0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 
	0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 
	0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 
	0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 
	0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 
	0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 
	0x0253, 0x0253, 0x0253, 0x0253, 0x0244, 0x0244, 0x0244, 0x0244, 
	0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 
	0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 
	0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 
	0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 
	0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 
	0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 
	0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 
	  -448, 0x04ff, 0x04ef, 0x04df, 0x04ee, 0x04cf, 0x04de, 0x04bf, 
	0x04fb, 0x04ce, 0x04dc,   -464, 0x03ec, 0x03ec, 0x03dd, 0x03dd, 
	0x04fa, 0x04cd, 0x03be, 0x03be, 0x03eb, 0x03eb, 0x039f, 0x039f, 
	0x03f9, 0x03f9, 0x03ea, 0x03ea, 0x03bd, 0x03bd, 0x03db, 0x03db, 
	0x038f, 0x038f, 0x03f8, 0x03f8, 0x03cc, 0x03cc, 0x04ae, 0x049e, 
	0x038e, 0x038e, 0x047f, 0x047e, 0x02f7, 0x02f7, 0x02f7, 0x02f7, 
	0x02da, 0x02da, 0x02da, 0x02da, 0x03ad, 0x03ad, 0x03bc, 0x03bc, 
	0x03cb, 0x03cb, 0x03f6, 0x03f6, 0x026f, 0x026f, 0x026f, 0x026f, 
	0x02e8, 0x02e8, 0x02e8, 0x02e8, 0x025f, 0x025f, 0x025f, 0x025f, 
	0x029d, 0x029d, 0x029d, 0x029d, 0x02d9, 0x02d9, 0x02d9, 0x02d9, 
	0x02f5, 0x02f5, 0x02f5, 0x02f5, 0x02e7, 0x02e7, 0x02e7, 0x02e7, 
	0x02ac, 0x02ac, 0x02ac, 0x02ac, 0x02bb, 0x02bb, 0x02bb, 0x02bb, 
	0x024f, 0x024f, 0x024f, 0x024f, 0x02f4, 0x02f4, 0x02f4, 0x02f4, 
	0x03ca, 0x03ca, 0x03e6, 0x03e6, 0x02f3, 0x02f3, 0x02f3, 0x02f3, 
	0x013f, 0x013f, 0x013f, 0x013f, 0x013f, 0x013f, 0x013f, 0x013f, 
	0x028d, 0x028d, 0x028d, 0x028d, 0x02d8, 0x02d8, 0x02d8, 0x02d8, 
	0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 
	0x01f2, 0x01f2, 0x01f2, 0x01f2, 0x01f2, 0x01f2, 0x01f2, 0x01f2, 
	0x026e, 0x026e, 0x026e, 0x026e, 0x029c, 0x029c, 0x029c, 0x029c, 
	0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 
	0x02c9, 0x02c9, 0x02c9, 0x02c9, 0x025e, 0x025e, 0x025e, 0x025e, 
	0x01ab, 0x01ab, 0x01ab, 0x01ab, 0x01ab, 0x01ab, 0x01ab, 0x01ab, 
	0x027d, 0x027d, 0x027d, 0x027d, 0x02d7, 0x02d7, 0x02d7, 0x02d7, 
	0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 
	0x02c8, 0x02c8, 0x02c8, 0x02c8, 0x02d6, 0x02d6, 0x02d6, 0x02d6, 
	0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 
	0x01b9, 0x01b9, 0x01b9, 0x01b9, 0x01b9, 0x01b9, 0x01b9, 0x01b9, 
	0x029b, 0x029b, 0x029b, 0x029b, 0x02aa, 0x02aa, 0x02aa, 0x02aa, 
	0x01ba, 0x01ba, 0x01ba, 0x01ba, 0x01ba, 0x01ba, 0x01ba, 0x01ba, 
	0x01e5, 0x01e5, 0x01e5, 0x01e5, 0x01e5, 0x01e5, 0x01e5, 0x01e5, 
	0x01e4, 0x01e4, 0x01e4, 0x01e4, 0x01e4, 0x01e4, 0x01e4, 0x01e4, 
	0x018c, 0x018c, 0x018c, 0x018c, 0x018c, 0x018c, 0x018c, 0x018c, 
	0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 
	0x01e3, 0x01e3, 0x01e3, 0x01e3, 0x01e3, 0x01e3, 0x01e3, 0x01e3, 
	0x012e, 0x012e, 0x012e, 0x012e, 0x012e, 0x012e, 0x012e, 0x012e, 
	0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 
	0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 
	0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 
	0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 
	0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 
	0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 
	0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 
	0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 
	0x01b8, 0x01b8, 0x01b8, 0x01b8, 0x01b8, 0x01b8, 0x01b8, 0x01b8, 
	0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 
	0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 
	0x01a9, 0x01a9, 0x01a9, 0x01a9, 0x01a9, 0x01a9, 0x01a9, 0x01a9, 
	0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 
	0x01d3, 0x01d3, 0x01d3, 0x01d3, 0x01d3, 0x01d3, 0x01d3, 0x01d3, 
	0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 
	0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 
	0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 
	0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 
	0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 
	0x01a7, 0x01a7, 0x01a7, 0x01a7, 0x01a7, 0x01a7, 0x01a7, 0x01a7, 
	0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 
	0x03fe, 0x03fe, 0x03fc, 0x03fc, 0x02fd, 0x02fd, 0x02fd, 0x02fd, 
	0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 
	0x01af, 0x01af, 0x01af, 0x01af, 0x01af, 0x01af, 0x01af, 0x01af, 
	0x01e9, 0x01e9, 0x01e9, 0x01e9, 0x01e9, 0x01e9, 0x01e9, 0x01e9, 
};

static const short tab15_[] =
{
	   -16,    -32,    -48,    -64,    -80,    -96,   -112,   -128, 
	  -144,   -160, 0x0311, 0x0311, 0x0401, 0x0410, 0x0300, 0x0300, 
	  -160,   -176,   -192,   -208,   -224,   -240,   -256,   -272, 
	  -288,   -304,   -320,   -336,   -352,   -368,   -384,   -400, 
	  -400,   -416,   -432,   -448,   -464,   -480,   -496,   -512, 
	  -528,   -544,   -560,   -576,   -592,   -608,   -624,   -640, 
	  -640,   -656, 0x0491,   -672,   -688,   -704,   -720,   -736, 
	0x0428, 0x0482, 0x0418, 0x0481,   -752,   -768,   -784,   -800, 
	0x0427, 0x0472, 0x0464, 0x0417, 0x0455, 0x0471,   -800, 0x0436, 
	0x0463, 0x0445, 0x0454, 0x0426, 0x0462, 0x0416,   -816, 0x0435, 
	0x0361, 0x0361, 0x0453, 0x0444, 0x0325, 0x0325, 0x0352, 0x0352, 
	0x0315, 0x0315, 0x0351, 0x0351, 0x0405, 0x0450, 0x0334, 0x0334, 
	0x0343, 0x0343, 0x0324, 0x0324, 0x0342, 0x0342, 0x0333, 0x0333, 
	0x0241, 0x0241, 0x0241, 0x0241, 0x0314, 0x0314, 0x0304, 0x0304, 
	0x0223, 0x0223, 0x0223, 0x0223, 0x0232, 0x0232, 0x0232, 0x0232, 
	0x0340, 0x0340, 0x0303, 0x0303, 0x0213, 0x0213, 0x0213, 0x0213, 
	0x0231, 0x0231, 0x0231, 0x0231, 0x0230, 0x0230, 0x0230, 0x0230, 
	0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 0x0122, 
	0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 
	0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 
	0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 0x0102, 
	0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 0x0120, 
	  -720,   -736, 0x04ee,   -752,   -768,   -784, 0x04fb,   -800, 
	0x04dd, 0x04af, 0x04fa, 0x04be, 0x04eb, 0x04cd, 0x04dc, 0x049f, 
	0x04f9, 0x04ea, 0x04bd, 0x04db, 0x048f, 0x04f8, 0x04cc, 0x049e, 
	0x04e9, 0x047f, 0x04f7, 0x04ad, 0x04da, 0x04bc, 0x046f,   -800, 
	0x03cb, 0x03cb, 0x03f6, 0x03f6, 0x048e, 0x04e8, 0x045f, 0x049d, 
	0x03f5, 0x03f5, 0x037e, 0x037e, 0x03e7, 0x03e7, 0x03ac, 0x03ac, 
	0x03ca, 0x03ca, 0x03bb, 0x03bb, 0x04d9, 0x048d, 0x034f, 0x034f, 
	0x03f4, 0x03f4, 0x033f, 0x033f, 0x03f3, 0x03f3, 0x03d8, 0x03d8, 
	0x03e6, 0x03e6, 0x032f, 0x032f, 0x03f2, 0x03f2, 0x046e, 0x04f0, 
	0x031f, 0x031f, 0x03f1, 0x03f1, 0x039c, 0x039c, 0x03c9, 0x03c9, 
	0x035e, 0x035e, 0x03ab, 0x03ab, 0x03ba, 0x03ba, 0x03e5, 0x03e5, 
	0x037d, 0x037d, 0x03d7, 0x03d7, 0x034e, 0x034e, 0x03e4, 0x03e4, 
	0x038c, 0x038c, 0x03c8, 0x03c8, 0x033e, 0x033e, 0x036d, 0x036d, 
	0x03d6, 0x03d6, 0x03e3, 0x03e3, 0x039b, 0x039b, 0x03b9, 0x03b9, 
	0x032e, 0x032e, 0x03aa, 0x03aa, 0x03e2, 0x03e2, 0x031e, 0x031e, 
	0x03e1, 0x03e1, 0x040e, 0x04e0, 0x035d, 0x035d, 0x03d5, 0x03d5, 
	0x037c, 0x037c, 0x03c7, 0x03c7, 0x034d, 0x034d, 0x038b, 0x038b, 
	0x02d4, 0x02d4, 0x02d4, 0x02d4, 0x03b8, 0x03b8, 0x039a, 0x039a, 
	0x03a9, 0x03a9, 0x036c, 0x036c, 0x03c6, 0x03c6, 0x033d, 0x033d, 
	0x02d3, 0x02d3, 0x02d3, 0x02d3, 0x02d2, 0x02d2, 0x02d2, 0x02d2, 
	0x032d, 0x032d, 0x030d, 0x030d, 0x021d, 0x021d, 0x021d, 0x021d, 
	0x027b, 0x027b, 0x027b, 0x027b, 0x02b7, 0x02b7, 0x02b7, 0x02b7, 
	0x02d1, 0x02d1, 0x02d1, 0x02d1, 0x035c, 0x035c, 0x03d0, 0x03d0, 
	0x02c5, 0x02c5, 0x02c5, 0x02c5, 0x028a, 0x028a, 0x028a, 0x028a, 
	0x02a8, 0x02a8, 0x02a8, 0x02a8, 0x024c, 0x024c, 0x024c, 0x024c, 
	0x02c4, 0x02c4, 0x02c4, 0x02c4, 0x026b, 0x026b, 0x026b, 0x026b, 
	0x02b6, 0x02b6, 0x02b6, 0x02b6, 0x0399, 0x0399, 0x030c, 0x030c, 
	0x023c, 0x023c, 0x023c, 0x023c, 0x02c3, 0x02c3, 0x02c3, 0x02c3, 
	0x027a, 0x027a, 0x027a, 0x027a, 0x02a7, 0x02a7, 0x02a7, 0x02a7, 
	0x02a6, 0x02a6, 0x02a6, 0x02a6, 0x03c0, 0x03c0, 0x030b, 0x030b, 
	0x01c2, 0x01c2, 0x01c2, 0x01c2, 0x01c2, 0x01c2, 0x01c2, 0x01c2, 
	0x022c, 0x022c, 0x022c, 0x022c, 0x025b, 0x025b, 0x025b, 0x025b, 
	0x02b5, 0x02b5, 0x02b5, 0x02b5, 0x021c, 0x021c, 0x021c, 0x021c, 
	0x0289, 0x0289, 0x0289, 0x0289, 0x0298, 0x0298, 0x0298, 0x0298, 
	0x02c1, 0x02c1, 0x02c1, 0x02c1, 0x024b, 0x024b, 0x024b, 0x024b, 
	0x02b4, 0x02b4, 0x02b4, 0x02b4, 0x026a, 0x026a, 0x026a, 0x026a, 
	0x023b, 0x023b, 0x023b, 0x023b, 0x0279, 0x0279, 0x0279, 0x0279, 
	0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 
	0x0297, 0x0297, 0x0297, 0x0297, 0x0288, 0x0288, 0x0288, 0x0288, 
	0x022b, 0x022b, 0x022b, 0x022b, 0x025a, 0x025a, 0x025a, 0x025a, 
	0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 
	0x02a5, 0x02a5, 0x02a5, 0x02a5, 0x021b, 0x021b, 0x021b, 0x021b, 
	0x01b1, 0x01b1, 0x01b1, 0x01b1, 0x01b1, 0x01b1, 0x01b1, 0x01b1, 
	0x02b0, 0x02b0, 0x02b0, 0x02b0, 0x0269, 0x0269, 0x0269, 0x0269, 
	0x0296, 0x0296, 0x0296, 0x0296, 0x024a, 0x024a, 0x024a, 0x024a, 
	0x02a4, 0x02a4, 0x02a4, 0x02a4, 0x0278, 0x0278, 0x0278, 0x0278, 
	0x0287, 0x0287, 0x0287, 0x0287, 0x023a, 0x023a, 0x023a, 0x023a, 
	0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 
	0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 
	0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 
	0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 
	0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 
	0x011a, 0x011a, 0x011a, 0x011a, 0x011a, 0x011a, 0x011a, 0x011a, 
	0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 
	0x020a, 0x020a, 0x020a, 0x020a, 0x02a0, 0x02a0, 0x02a0, 0x02a0, 
	0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 
	0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 
	0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 
	0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 
	0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 
	0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 
	0x0277, 0x0277, 0x0277, 0x0277, 0x0209, 0x0209, 0x0209, 0x0209, 
	0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 
	0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 
	0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 
	0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 
	0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 
	0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 
	0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 
	0x0190, 0x0190, 0x0190, 0x0190, 0x0190, 0x0190, 0x0190, 0x0190, 
	0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 
	0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 
	0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 
	0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 
	0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 
	0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 
	0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 
	0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 
	0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 
	0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 0x0108, 
	0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 
	0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 
	0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 
	0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 
	0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 0x0173, 
	0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 0x0146, 
	0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 0x0107, 
	0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 0x0170, 
	0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 
	0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 
	0x01ff, 0x01ff, 0x01ff, 0x01ff, 0x01ff, 0x01ff, 0x01ff, 0x01ff, 
	0x01ef, 0x01ef, 0x01ef, 0x01ef, 0x01ef, 0x01ef, 0x01ef, 0x01ef, 
	0x01fe, 0x01fe, 0x01fe, 0x01fe, 0x01fe, 0x01fe, 0x01fe, 0x01fe, 
	0x01df, 0x01df, 0x01df, 0x01df, 0x01df, 0x01df, 0x01df, 0x01df, 
	0x01fd, 0x01fd, 0x01fd, 0x01fd, 0x01fd, 0x01fd, 0x01fd, 0x01fd, 
	0x01cf, 0x01cf, 0x01cf, 0x01cf, 0x01cf, 0x01cf, 0x01cf, 0x01cf, 
	0x01fc, 0x01fc, 0x01fc, 0x01fc, 0x01fc, 0x01fc, 0x01fc, 0x01fc, 
	0x01de, 0x01de, 0x01de, 0x01de, 0x01de, 0x01de, 0x01de, 0x01de, 
	0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 0x01ed, 
	0x01bf, 0x01bf, 0x01bf, 0x01bf, 0x01bf, 0x01bf, 0x01bf, 0x01bf, 
	0x01ce, 0x01ce, 0x01ce, 0x01ce, 0x01ce, 0x01ce, 0x01ce, 0x01ce, 
	0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 
	0x01ae, 0x01ae, 0x01ae, 0x01ae, 0x01ae, 0x01ae, 0x01ae, 0x01ae, 
	0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 
};

static const short tab16_[] =
{
	   -16,    -32,    -48,    -64, 0x0411, 0x0401, 0x0310, 0x0310, 
	0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 0x0100, 
	   -64,    -80,    -96, 0x04ff,   -112,   -128,   -144, 0x04f2, 
	  -160, 0x041f, 0x04f1,   -176,   -192,   -208,   -224,   -240, 
	  -240,   -256,   -272,   -288,   -304,   -320,   -336,   -352, 
	  -368,   -384,   -400,   -416,   -432,   -448, 0x0451,   -464, 
	  -464,   -480,   -496, 0x0414, 0x0441,   -512, 0x0423, 0x0432, 
	0x0313, 0x0313, 0x0331, 0x0331, 0x0403, 0x0430, 0x0322, 0x0322, 
	0x0212, 0x0212, 0x0212, 0x0212, 0x0221, 0x0221, 0x0221, 0x0221, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220, 
	0x03ef, 0x03ef, 0x03fe, 0x03fe, 0x03df, 0x03df, 0x03fd, 0x03fd, 
	0x03cf, 0x03cf, 0x03fc, 0x03fc, 0x03bf, 0x03bf, 0x03fb, 0x03fb, 
	0x02af, 0x02af, 0x02af, 0x02af, 0x03fa, 0x03fa, 0x039f, 0x039f, 
	0x03f9, 0x03f9, 0x03f8, 0x03f8, 0x028f, 0x028f, 0x028f, 0x028f, 
	0x027f, 0x027f, 0x027f, 0x027f, 0x02f7, 0x02f7, 0x02f7, 0x02f7, 
	0x026f, 0x026f, 0x026f, 0x026f, 0x02f6, 0x02f6, 0x02f6, 0x02f6, 
	0x025f, 0x025f, 0x025f, 0x025f, 0x02f5, 0x02f5, 0x02f5, 0x02f5, 
	0x014f, 0x014f, 0x014f, 0x014f, 0x014f, 0x014f, 0x014f, 0x014f, 
	0x01f4, 0x01f4, 0x01f4, 0x01f4, 0x01f4, 0x01f4, 0x01f4, 0x01f4, 
	0x01f3, 0x01f3, 0x01f3, 0x01f3, 0x01f3, 0x01f3, 0x01f3, 0x01f3, 
	0x01f0, 0x01f0, 0x01f0, 0x01f0, 0x01f0, 0x01f0, 0x01f0, 0x01f0, 
	0x023f, 0x023f, 0x023f, 0x023f,   -416,   -432,   -448,   -464, 
	0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 0x012f, 
	0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 
	  -448,   -464,   -480,   -496,   -512,   -528,   -544,   -560, 
	  -576,   -592,   -608, 0x04e3,   -624,   -640,   -656,   -672, 
	  -672,   -688,   -704, 0x040d,   -720,   -736,   -752, 0x043c, 
	  -768, 0x041c, 0x04c0,   -784, 0x03e2, 0x03e2, 0x042e, 0x041e, 
	0x04d3, 0x042d, 0x04d2, 0x04d1, 0x043b,   -784, 0x031d, 0x031d, 
	0x04c4, 0x046b, 0x04c3, 0x04a7, 0x032c, 0x032c, 0x04c2, 0x04b5, 
	0x04c1, 0x040c, 0x044b, 0x04b4, 0x046a, 0x04a6, 0x03b3, 0x03b3, 
	0x045a, 0x04a5, 0x032b, 0x032b, 0x03b2, 0x03b2, 0x031b, 0x031b, 
	0x03b1, 0x03b1, 0x040b, 0x04b0, 0x0469, 0x0496, 0x044a, 0x04a4, 
	0x0478, 0x0487, 0x03a3, 0x03a3, 0x043a, 0x0459, 0x032a, 0x032a, 
	0x0495, 0x0468, 0x03a1, 0x03a1, 0x0486, 0x0477, 0x0394, 0x0394, 
	0x0449, 0x0457, 0x0367, 0x0367, 0x02a2, 0x02a2, 0x02a2, 0x02a2, 
	0x021a, 0x021a, 0x021a, 0x021a, 0x030a, 0x030a, 0x03a0, 0x03a0, 
	0x0339, 0x0339, 0x0393, 0x0393, 0x0358, 0x0358, 0x0385, 0x0385, 
	0x0229, 0x0229, 0x0229, 0x0229, 0x0292, 0x0292, 0x0292, 0x0292, 
	0x0376, 0x0376, 0x0309, 0x0309, 0x0219, 0x0219, 0x0219, 0x0219, 
	0x0291, 0x0291, 0x0291, 0x0291, 0x0390, 0x0390, 0x0348, 0x0348, 
	0x0384, 0x0384, 0x0375, 0x0375, 0x0338, 0x0338, 0x0383, 0x0383, 
	0x0366, 0x0366, 0x0328, 0x0328, 0x0282, 0x0282, 0x0282, 0x0282, 
	0x0347, 0x0347, 0x0374, 0x0374, 0x0218, 0x0218, 0x0218, 0x0218, 
	0x0281, 0x0281, 0x0281, 0x0281, 0x0280, 0x0280, 0x0280, 0x0280, 
	0x0308, 0x0308, 0x0356, 0x0356, 0x0237, 0x0237, 0x0237, 0x0237, 
	0x0273, 0x0273, 0x0273, 0x0273, 0x0365, 0x0365, 0x0346, 0x0346, 
	0x0227, 0x0227, 0x0227, 0x0227, 0x0272, 0x0272, 0x0272, 0x0272, 
	0x0364, 0x0364, 0x0355, 0x0355, 0x0207, 0x0207, 0x0207, 0x0207, 
	0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 
	0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 0x0171, 
	0x0270, 0x0270, 0x0270, 0x0270, 0x0236, 0x0236, 0x0236, 0x0236, 
	0x0263, 0x0263, 0x0263, 0x0263, 0x0245, 0x0245, 0x0245, 0x0245, 
	0x0254, 0x0254, 0x0254, 0x0254, 0x0226, 0x0226, 0x0226, 0x0226, 
	0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 0x0162, 
	0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 0x0116, 
	0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 0x0161, 
	0x0206, 0x0206, 0x0206, 0x0206, 0x0260, 0x0260, 0x0260, 0x0260, 
	0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 0x0153, 
	0x0235, 0x0235, 0x0235, 0x0235, 0x0244, 0x0244, 0x0244, 0x0244, 
	0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 0x0125, 
	0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 0x0152, 
	0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 0x0115, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 0x0134, 
	0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 0x0143, 
	0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 
	0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 0x0124, 
	0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 0x0142, 
	0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 0x0133, 
	0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 0x0104, 
	0x0140, 0x0140, 0x0140, 0x0140, 0x0140, 0x0140, 0x0140, 0x0140, 
	0x04ce,   -448, 0x03de, 0x03de, 0x03e9, 0x03e9, 0x04ea, 0x04d9, 
	0x02ee, 0x02ee, 0x02ee, 0x02ee, 0x03ed, 0x03ed, 0x03eb, 0x03eb, 
	0x02be, 0x02be, 0x02be, 0x02be, 0x02cd, 0x02cd, 0x02cd, 0x02cd, 
	0x03dc, 0x03dc, 0x03db, 0x03db, 0x02ae, 0x02ae, 0x02ae, 0x02ae, 
	0x02cc, 0x02cc, 0x02cc, 0x02cc, 0x03ad, 0x03ad, 0x03da, 0x03da, 
	0x037e, 0x037e, 0x03ac, 0x03ac, 0x02ca, 0x02ca, 0x02ca, 0x02ca, 
	0x03c9, 0x03c9, 0x037d, 0x037d, 0x025e, 0x025e, 0x025e, 0x025e, 
	0x01bd, 0x01bd, 0x01bd, 0x01bd, 0x01bd, 0x01bd, 0x01bd, 0x01bd, 
	0x019e, 0x019e, 0x019e, 0x019e, 0x019e, 0x019e, 0x019e, 0x019e, 
	0x02bc, 0x02bc, 0x02bc, 0x02bc, 0x02cb, 0x02cb, 0x02cb, 0x02cb, 
	0x028e, 0x028e, 0x028e, 0x028e, 0x02e8, 0x02e8, 0x02e8, 0x02e8, 
	0x029d, 0x029d, 0x029d, 0x029d, 0x02e7, 0x02e7, 0x02e7, 0x02e7, 
	0x02bb, 0x02bb, 0x02bb, 0x02bb, 0x028d, 0x028d, 0x028d, 0x028d, 
	0x02d8, 0x02d8, 0x02d8, 0x02d8, 0x026e, 0x026e, 0x026e, 0x026e, 
	0x01e6, 0x01e6, 0x01e6, 0x01e6, 0x01e6, 0x01e6, 0x01e6, 0x01e6, 
	0x019c, 0x019c, 0x019c, 0x019c, 0x019c, 0x019c, 0x019c, 0x019c, 
	0x02ab, 0x02ab, 0x02ab, 0x02ab, 0x02ba, 0x02ba, 0x02ba, 0x02ba, 
	0x02e5, 0x02e5, 0x02e5, 0x02e5, 0x02d7, 0x02d7, 0x02d7, 0x02d7, 
	0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 0x014e, 
	0x02e4, 0x02e4, 0x02e4, 0x02e4, 0x028c, 0x028c, 0x028c, 0x028c, 
	0x01c8, 0x01c8, 0x01c8, 0x01c8, 0x01c8, 0x01c8, 0x01c8, 0x01c8, 
	0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 0x013e, 
	0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 0x016d, 
	0x02d6, 0x02d6, 0x02d6, 0x02d6, 0x029b, 0x029b, 0x029b, 0x029b, 
	0x02b9, 0x02b9, 0x02b9, 0x02b9, 0x02aa, 0x02aa, 0x02aa, 0x02aa, 
	0x01e1, 0x01e1, 0x01e1, 0x01e1, 0x01e1, 0x01e1, 0x01e1, 0x01e1, 
	0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 0x01d4, 
	0x02b8, 0x02b8, 0x02b8, 0x02b8, 0x02a9, 0x02a9, 0x02a9, 0x02a9, 
	0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 0x017b, 
	0x02b7, 0x02b7, 0x02b7, 0x02b7, 0x02d0, 0x02d0, 0x02d0, 0x02d0, 
	0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 0x010e, 
	0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 0x01e0, 
	0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 0x015d, 
	0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 0x01d5, 
	0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 0x017c, 
	0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 0x01c7, 
	0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 0x014d, 
	0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 0x018b, 
	0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 0x019a, 
	0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 0x016c, 
	0x01c6, 0x01c6, 0x01c6, 0x01c6, 0x01c6, 0x01c6, 0x01c6, 0x01c6, 
	0x013d, 0x013d, 0x013d, 0x013d, 0x013d, 0x013d, 0x013d, 0x013d, 
	0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 0x015c, 
	0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 0x01c5, 
	0x018a, 0x018a, 0x018a, 0x018a, 0x018a, 0x018a, 0x018a, 0x018a, 
	0x01a8, 0x01a8, 0x01a8, 0x01a8, 0x01a8, 0x01a8, 0x01a8, 0x01a8, 
	0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 0x0199, 
	0x014c, 0x014c, 0x014c, 0x014c, 0x014c, 0x014c, 0x014c, 0x014c, 
	0x01b6, 0x01b6, 0x01b6, 0x01b6, 0x01b6, 0x01b6, 0x01b6, 0x01b6, 
	0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 0x017a, 
	0x015b, 0x015b, 0x015b, 0x015b, 0x015b, 0x015b, 0x015b, 0x015b, 
	0x0189, 0x0189, 0x0189, 0x0189, 0x0189, 0x0189, 0x0189, 0x0189, 
	0x0198, 0x0198, 0x0198, 0x0198, 0x0198, 0x0198, 0x0198, 0x0198, 
	0x0179, 0x0179, 0x0179, 0x0179, 0x0179, 0x0179, 0x0179, 0x0179, 
	0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 0x0197, 
	0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 
	0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 0x01ec, 
	0x01dd, 0x01dd, 0x01dd, 0x01dd, 0x01dd, 0x01dd, 0x01dd, 0x01dd, 
};

static const short tab24_[] =
{
	   -16,    -32,    -48, 0x04ff,    -64,    -80,    -96,   -112, 
	  -128,   -144,   -160,   -176, 0x0411, 0x0401, 0x0410, 0x0400, 
	0x04ef, 0x04fe, 0x04df, 0x04fd, 0x04cf, 0x04fc, 0x04bf, 0x04fb, 
	0x03fa, 0x03fa, 0x04af, 0x049f, 0x03f9, 0x03f9, 0x03f8, 0x03f8, 
	0x048f, 0x047f, 0x03f7, 0x03f7, 0x036f, 0x036f, 0x03f6, 0x03f6, 
	0x035f, 0x035f, 0x03f5, 0x03f5, 0x034f, 0x034f, 0x03f4, 0x03f4, 
	0x033f, 0x033f, 0x03f3, 0x03f3, 0x032f, 0x032f, 0x03f2, 0x03f2, 
	0x03f1, 0x03f1, 0x041f, 0x04f0,   -144,   -160,   -176,   -192, 
	  -192,   -208,   -224,   -240,   -256,   -272,   -288,   -304, 
	  -320,   -336,   -352,   -368,   -384,   -400,   -416,   -432, 
	  -432,   -448,   -464,   -480,   -496,   -512,   -528,   -544, 
	  -560,   -576,   -592,   -608,   -624,   -640,   -656,   -672, 
	  -672,   -688,   -704,   -720,   -736,   -752,   -768,   -784, 
	  -800,   -816,   -832,   -848,   -864, 0x0473,   -880, 0x0472, 
	0x0446, 0x0464, 0x0455, 0x0471, 0x0436, 0x0463, 0x0445, 0x0454, 
	0x0426, 0x0462, 0x0416, 0x0461,   -880, 0x0435, 0x0453, 0x0444, 
	0x0425, 0x0452, 0x0415,   -880, 0x0351, 0x0351, 0x0434, 0x0443, 
	0x0324, 0x0324, 0x0342, 0x0342, 0x0333, 0x0333, 0x0314, 0x0314, 
	0x0341, 0x0341, 0x0404, 0x0440, 0x0323, 0x0323, 0x0332, 0x0332, 
	0x0213, 0x0213, 0x0213, 0x0213, 0x0231, 0x0231, 0x0231, 0x0231, 
	0x0303, 0x0303, 0x0330, 0x0330, 0x0222, 0x0222, 0x0222, 0x0222, 
	0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 0x0112, 
	0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 0x0121, 
	0x0202, 0x0202, 0x0202, 0x0202, 0x0220, 0x0220, 0x0220, 0x0220, 
	0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 0x010f, 
	0x03ee, 0x03ee, 0x03de, 0x03de, 0x03ed, 0x03ed, 0x03ce, 0x03ce, 
	0x03ec, 0x03ec, 0x03dd, 0x03dd, 0x03be, 0x03be, 0x03eb, 0x03eb, 
	0x03cd, 0x03cd, 0x03dc, 0x03dc, 0x03ae, 0x03ae, 0x03ea, 0x03ea, 
	0x03bd, 0x03bd, 0x03db, 0x03db, 0x03cc, 0x03cc, 0x039e, 0x039e, 
	0x03e9, 0x03e9, 0x03ad, 0x03ad, 0x03da, 0x03da, 0x03bc, 0x03bc, 
	0x03cb, 0x03cb, 0x038e, 0x038e, 0x03e8, 0x03e8, 0x039d, 0x039d, 
	0x03d9, 0x03d9, 0x037e, 0x037e, 0x03e7, 0x03e7, 0x03ac, 0x03ac, 
	0x03ca, 0x03ca, 0x03bb, 0x03bb, 0x038d, 0x038d, 0x03d8, 0x03d8, 
	0x040e, 0x04e0, 0x030d, 0x030d, 0x02e6, 0x02e6, 0x02e6, 0x02e6, 
	0x036e, 0x036e, 0x039c, 0x039c, 0x02c9, 0x02c9, 0x02c9, 0x02c9, 
	0x025e, 0x025e, 0x025e, 0x025e, 0x02ba, 0x02ba, 0x02ba, 0x02ba, 
	0x02e5, 0x02e5, 0x02e5, 0x02e5, 0x03ab, 0x03ab, 0x037d, 0x037d, 
	0x02d7, 0x02d7, 0x02d7, 0x02d7, 0x02e4, 0x02e4, 0x02e4, 0x02e4, 
	0x028c, 0x028c, 0x028c, 0x028c, 0x02c8, 0x02c8, 0x02c8, 0x02c8, 
	0x034e, 0x034e, 0x032e, 0x032e, 0x023e, 0x023e, 0x023e, 0x023e, 
	0x026d, 0x026d, 0x026d, 0x026d, 0x02d6, 0x02d6, 0x02d6, 0x02d6, 
	0x02e3, 0x02e3, 0x02e3, 0x02e3, 0x029b, 0x029b, 0x029b, 0x029b, 
	0x02b9, 0x02b9, 0x02b9, 0x02b9, 0x02aa, 0x02aa, 0x02aa, 0x02aa, 
	0x02e2, 0x02e2, 0x02e2, 0x02e2, 0x021e, 0x021e, 0x021e, 0x021e, 
	0x02e1, 0x02e1, 0x02e1, 0x02e1, 0x025d, 0x025d, 0x025d, 0x025d, 
	0x02d5, 0x02d5, 0x02d5, 0x02d5, 0x027c, 0x027c, 0x027c, 0x027c, 
	0x02c7, 0x02c7, 0x02c7, 0x02c7, 0x024d, 0x024d, 0x024d, 0x024d, 
	0x028b, 0x028b, 0x028b, 0x028b, 0x02b8, 0x02b8, 0x02b8, 0x02b8, 
	0x02d4, 0x02d4, 0x02d4, 0x02d4, 0x029a, 0x029a, 0x029a, 0x029a, 
	0x02a9, 0x02a9, 0x02a9, 0x02a9, 0x026c, 0x026c, 0x026c, 0x026c, 
	0x02c6, 0x02c6, 0x02c6, 0x02c6, 0x023d, 0x023d, 0x023d, 0x023d, 
	0x02d3, 0x02d3, 0x02d3, 0x02d3, 0x022d, 0x022d, 0x022d, 0x022d, 
	0x02d2, 0x02d2, 0x02d2, 0x02d2, 0x021d, 0x021d, 0x021d, 0x021d, 
	0x027b, 0x027b, 0x027b, 0x027b, 0x02b7, 0x02b7, 0x02b7, 0x02b7, 
	0x02d1, 0x02d1, 0x02d1, 0x02d1, 0x025c, 0x025c, 0x025c, 0x025c, 
	0x02c5, 0x02c5, 0x02c5, 0x02c5, 0x028a, 0x028a, 0x028a, 0x028a, 
	0x02a8, 0x02a8, 0x02a8, 0x02a8, 0x0299, 0x0299, 0x0299, 0x0299, 
	0x024c, 0x024c, 0x024c, 0x024c, 0x02c4, 0x02c4, 0x02c4, 0x02c4, 
	0x026b, 0x026b, 0x026b, 0x026b, 0x02b6, 0x02b6, 0x02b6, 0x02b6, 
	0x03d0, 0x03d0, 0x030c, 0x030c, 0x023c, 0x023c, 0x023c, 0x023c, 
	0x02c3, 0x02c3, 0x02c3, 0x02c3, 0x027a, 0x027a, 0x027a, 0x027a, 
	0x02a7, 0x02a7, 0x02a7, 0x02a7, 0x022c, 0x022c, 0x022c, 0x022c, 
	0x02c2, 0x02c2, 0x02c2, 0x02c2, 0x025b, 0x025b, 0x025b, 0x025b, 
	0x02b5, 0x02b5, 0x02b5, 0x02b5, 0x021c, 0x021c, 0x021c, 0x021c, 
	0x0289, 0x0289, 0x0289, 0x0289, 0x0298, 0x0298, 0x0298, 0x0298, 
	0x02c1, 0x02c1, 0x02c1, 0x02c1, 0x024b, 0x024b, 0x024b, 0x024b, 
	0x03c0, 0x03c0, 0x030b, 0x030b, 0x023b, 0x023b, 0x023b, 0x023b, 
	0x03b0, 0x03b0, 0x030a, 0x030a, 0x021a, 0x021a, 0x021a, 0x021a, 
	0x01b4, 0x01b4, 0x01b4, 0x01b4, 0x01b4, 0x01b4, 0x01b4, 0x01b4, 
	0x026a, 0x026a, 0x026a, 0x026a, 0x02a6, 0x02a6, 0x02a6, 0x02a6, 
	0x0279, 0x0279, 0x0279, 0x0279, 0x0297, 0x0297, 0x0297, 0x0297, 
	0x03a0, 0x03a0, 0x0309, 0x0309, 0x0290, 0x0290, 0x0290, 0x0290, 
	0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 0x01b3, 
	0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 0x0188, 
	0x022b, 0x022b, 0x022b, 0x022b, 0x025a, 0x025a, 0x025a, 0x025a, 
	0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 0x01b2, 
	0x02a5, 0x02a5, 0x02a5, 0x02a5, 0x021b, 0x021b, 0x021b, 0x021b, 
	0x02b1, 0x02b1, 0x02b1, 0x02b1, 0x0269, 0x0269, 0x0269, 0x0269, 
	0x0196, 0x0196, 0x0196, 0x0196, 0x0196, 0x0196, 0x0196, 0x0196, 
	0x01a4, 0x01a4, 0x01a4, 0x01a4, 0x01a4, 0x01a4, 0x01a4, 0x01a4, 
	0x024a, 0x024a, 0x024a, 0x024a, 0x0278, 0x0278, 0x0278, 0x0278, 
	0x0187, 0x0187, 0x0187, 0x0187, 0x0187, 0x0187, 0x0187, 0x0187, 
	0x013a, 0x013a, 0x013a, 0x013a, 0x013a, 0x013a, 0x013a, 0x013a, 
	0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 0x01a3, 
	0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 0x0159, 
	0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 0x0195, 
	0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 0x012a, 
	0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 0x01a2, 
	0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 0x01a1, 
	0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 0x0168, 
	0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 0x0186, 
	0x0177, 0x0177, 0x0177, 0x0177, 0x0177, 0x0177, 0x0177, 0x0177, 
	0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 0x0149, 
	0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 0x0194, 
	0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 0x0139, 
	0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 0x0193, 
	0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 0x0158, 
	0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 0x0185, 
	0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 0x0129, 
	0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 0x0167, 
	0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 0x0176, 
	0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 0x0192, 
	0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 0x0119, 
	0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 0x0191, 
	0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 0x0148, 
	0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 0x0184, 
	0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 0x0157, 
	0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 0x0175, 
	0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 0x0138, 
	0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 0x0183, 
	0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 0x0166, 
	0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 0x0128, 
	0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 0x0182, 
	0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 0x0118, 
	0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 0x0147, 
	0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 0x0174, 
	0x0181, 0x0181, 0x0181, 0x0181, 0x0181, 0x0181, 0x0181, 0x0181, 
	0x0208, 0x0208, 0x0208, 0x0208, 0x0280, 0x0280, 0x0280, 0x0280, 
	0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 0x0156, 
	0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 0x0165, 
	0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 0x0117, 
	0x0207, 0x0207, 0x0207, 0x0207, 0x0270, 0x0270, 0x0270, 0x0270, 
	0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 0x0137, 
	0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 0x0127, 
	0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 0x0106, 
	0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 0x0160, 
	0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 0x0105, 
	0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 0x0150, 
};

static const short tab_c0[] =
{
 -29, -21, -13,  -7,  -3,  -1,  11,  15,  -1,  13,  14,  -3,  -1,   7,   5,
   9,  -3,  -1,   6,   3,  -1,  10,  12,  -3,  -1,   2,   1,  -1,   4,   8,
   0
};

static const short tab_c1[] =
{
 -15,  -7,  -3,  -1,  15,  14,  -1,  13,  12,  -3,  -1,  11,  10,  -1,   9,
   8,  -7,  -3,  -1,   7,   6,  -1,   5,   4,  -3,  -1,   3,   2,  -1,   1,
   0
};

static const struct newhuff ht[] = 
{
 { /* 0 */ 0 , tab0_  } ,
 { /* 2 */ 0 , tab1_  } ,
 { /* 3 */ 0 , tab2_  } ,
 { /* 3 */ 0 , tab3_  } ,
 { /* 0 */ 0 , tab0_  } ,
 { /* 4 */ 0 , tab5_  } ,
 { /* 4 */ 0 , tab6_  } ,
 { /* 6 */ 0 , tab7_  } ,
 { /* 6 */ 0 , tab8_  } ,
 { /* 6 */ 0 , tab9_  } ,
 { /* 8 */ 0 , tab10_ } ,
 { /* 8 */ 0 , tab11_ } ,
 { /* 8 */ 0 , tab12_ } ,
 { /* 16 */ 0 , tab13_ } ,
 { /* 0  */ 0 , tab0_  } ,
 { /* 16 */ 0 , tab15_ } ,

 { /* 16 */ 1 , tab16_ } ,
 { /* 16 */ 2 , tab16_ } ,
 { /* 16 */ 3 , tab16_ } ,
 { /* 16 */ 4 , tab16_ } ,
 { /* 16 */ 6 , tab16_ } ,
 { /* 16 */ 8 , tab16_ } ,
 { /* 16 */ 10, tab16_ } ,
 { /* 16 */ 13, tab16_ } ,
 { /* 16 */ 4 , tab24_ } ,
 { /* 16 */ 5 , tab24_ } ,
 { /* 16 */ 6 , tab24_ } ,
 { /* 16 */ 7 , tab24_ } ,
 { /* 16 */ 8 , tab24_ } ,
 { /* 16 */ 9 , tab24_ } ,
 { /* 16 */ 11, tab24_ } ,
 { /* 16 */ 13, tab24_ }
};

static const struct newhuff htc[] = 
{
 { /* 1 , 1 , */ 0 , tab_c0 } ,
 { /* 1 , 1 , */ 0 , tab_c1 }
};

#endif

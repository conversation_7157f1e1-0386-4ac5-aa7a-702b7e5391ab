#include <stdio.h>
#include "lcd_i80.h"
#include "driver/ledc.h"
#include "board_pins_user.h"

#define TAG "i80 LCD"

#ifndef DEF_LCD_BLK_GPIO
#define LCD_I80_PIN_NUM_BK_LIGHT CONFIG_LCD_I80_PIN_NUM_BK_LIGHT
#else
#define LCD_I80_PIN_NUM_BK_LIGHT DEF_LCD_BLK_GPIO
#endif

#ifndef DEF_LCD_CS_GPIO
#define LCD_I80_PIN_NUM_CS CONFIG_LCD_I80_PIN_NUM_CS
#else
#define LCD_I80_PIN_NUM_CS DEF_LCD_CS_GPIO
#endif

#ifndef DEF_LCD_RST_GPIO
#define LCD_I80_PIN_NUM_RST CONFIG_LCD_I80_PIN_NUM_RST
#else
#define LCD_I80_PIN_NUM_RST DEF_LCD_RST_GPIO
#endif

#ifndef DEF_LCD_DC_GPIO
#define LCD_I80_PIN_NUM_DC CONFIG_LCD_I80_PIN_NUM_DC
#else
#define LCD_I80_PIN_NUM_DC DEF_LCD_DC_GPIO
#endif

#ifndef DEF_LCD_PCLK_GPIO
#define LCD_I80_PIN_NUM_PCLK CONFIG_LCD_I80_PIN_NUM_PCLK
#else
#define LCD_I80_PIN_NUM_PCLK DEF_LCD_PCLK_GPIO
#endif

#ifndef DEF_LCD_DB0_GPIO
#define LCD_I80_PIN_NUM_DATA0 CONFIG_LCD_I80_PIN_NUM_DATA0
#else
#define LCD_I80_PIN_NUM_DATA0 DEF_LCD_DB0_GPIO
#endif

#ifndef DEF_LCD_DB1_GPIO
#define LCD_I80_PIN_NUM_DATA1 CONFIG_LCD_I80_PIN_NUM_DATA1
#else
#define LCD_I80_PIN_NUM_DATA1 DEF_LCD_DB1_GPIO
#endif

#ifndef DEF_LCD_DB2_GPIO
#define LCD_I80_PIN_NUM_DATA2 CONFIG_LCD_I80_PIN_NUM_DATA2
#else
#define LCD_I80_PIN_NUM_DATA2 DEF_LCD_DB2_GPIO
#endif

#ifndef DEF_LCD_DB3_GPIO
#define LCD_I80_PIN_NUM_DATA3 CONFIG_LCD_I80_PIN_NUM_DATA3
#else
#define LCD_I80_PIN_NUM_DATA3 DEF_LCD_DB3_GPIO
#endif

#ifndef DEF_LCD_DB4_GPIO
#define LCD_I80_PIN_NUM_DATA4 CONFIG_LCD_I80_PIN_NUM_DATA4
#else
#define LCD_I80_PIN_NUM_DATA4 DEF_LCD_DB4_GPIO
#endif

#ifndef DEF_LCD_DB5_GPIO
#define LCD_I80_PIN_NUM_DATA5 CONFIG_LCD_I80_PIN_NUM_DATA5
#else
#define LCD_I80_PIN_NUM_DATA5 DEF_LCD_DB5_GPIO
#endif

#ifndef DEF_LCD_DB6_GPIO
#define LCD_I80_PIN_NUM_DATA6 CONFIG_LCD_I80_PIN_NUM_DATA6
#else
#define LCD_I80_PIN_NUM_DATA6 DEF_LCD_DB6_GPIO
#endif

#ifndef DEF_LCD_DB7_GPIO
#define LCD_I80_PIN_NUM_DATA7 CONFIG_LCD_I80_PIN_NUM_DATA7
#else
#define LCD_I80_PIN_NUM_DATA7 DEF_LCD_DB7_GPIO
#endif

#if CONFIG_LCD_I80_LCD_I80_BUS_WIDTH > 8

#ifndef DEF_LCD_DB8_GPIO
#define LCD_I80_PIN_NUM_DATA8 CONFIG_LCD_I80_PIN_NUM_DATA8
#else
#define LCD_I80_PIN_NUM_DATA8 DEF_LCD_DB8_GPIO
#endif

#ifndef DEF_LCD_DB9_GPIO
#define LCD_I80_PIN_NUM_DATA9 CONFIG_LCD_I80_PIN_NUM_DATA9
#else
#define LCD_I80_PIN_NUM_DATA9 DEF_LCD_DB9_GPIO
#endif

#ifndef DEF_LCD_DB10_GPIO
#define LCD_I80_PIN_NUM_DATA10 CONFIG_LCD_I80_PIN_NUM_DATA10
#else
#define LCD_I80_PIN_NUM_DATA10 DEF_LCD_DB10_GPIO
#endif

#ifndef DEF_LCD_DB11_GPIO
#define LCD_I80_PIN_NUM_DATA11 CONFIG_LCD_I80_PIN_NUM_DATA11
#else
#define LCD_I80_PIN_NUM_DATA11 DEF_LCD_DB11_GPIO
#endif

#ifndef DEF_LCD_DB12_GPIO
#define LCD_I80_PIN_NUM_DATA12 CONFIG_LCD_I80_PIN_NUM_DATA12
#else
#define LCD_I80_PIN_NUM_DATA12 DEF_LCD_DB12_GPIO
#endif

#ifndef DEF_LCD_DB13_GPIO
#define LCD_I80_PIN_NUM_DATA13 CONFIG_LCD_I80_PIN_NUM_DATA13
#else
#define LCD_I80_PIN_NUM_DATA13 DEF_LCD_DB13_GPIO
#endif

#ifndef DEF_LCD_DB14_GPIO
#define LCD_I80_PIN_NUM_DATA14 CONFIG_LCD_I80_PIN_NUM_DATA14
#else
#define LCD_I80_PIN_NUM_DATA14 DEF_LCD_DB14_GPIO
#endif

#ifndef DEF_LCD_DB15_GPIO
#define LCD_I80_PIN_NUM_DATA15 CONFIG_LCD_I80_PIN_NUM_DATA15
#else
#define LCD_I80_PIN_NUM_DATA15 DEF_LCD_DB15_GPIO
#endif

#endif

#if (CONFIG_LCD_I80_CONTROL_BACK_LIGHT_USED)
ledc_channel_config_t ledc_cConfig;
ledc_timer_config_t ledc_tConfig;
#endif

#if (CONFIG_LCD_I80_CONTROL_BACK_LIGHT_USED)
static void _ledc_pwm_duty_set(uint8_t duty);
static void _ledc_channel_config(void);
static void _ledc_timer_config(void);
#endif

static bool lcd_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
{
    lcd_i80_t *lcd = (lcd_i80_t *)user_ctx;

    if (lcd->transcallback)
        lcd->transcallback();
    return false;
}

static void lcd_i80_bus_init(lcd_i80_t *lcd)
{
    ESP_LOGI(TAG, "Initialize Intel 8080 bus");
    esp_lcd_i80_bus_handle_t i80_bus = NULL;
    esp_lcd_i80_bus_config_t bus_config = {
        .clk_src = LCD_CLK_SRC_DEFAULT,
        .dc_gpio_num = LCD_I80_PIN_NUM_DC,
        .wr_gpio_num = LCD_I80_PIN_NUM_PCLK,
        .data_gpio_nums = {
            LCD_I80_PIN_NUM_DATA0,
            LCD_I80_PIN_NUM_DATA1,
            LCD_I80_PIN_NUM_DATA2,
            LCD_I80_PIN_NUM_DATA3,
            LCD_I80_PIN_NUM_DATA4,
            LCD_I80_PIN_NUM_DATA5,
            LCD_I80_PIN_NUM_DATA6,
            LCD_I80_PIN_NUM_DATA7,
#if CONFIG_LCD_I80_LCD_I80_BUS_WIDTH > 8
            LCD_I80_PIN_NUM_DATA8,
            LCD_I80_PIN_NUM_DATA9,
            LCD_I80_PIN_NUM_DATA10,
            LCD_I80_PIN_NUM_DATA11,
            LCD_I80_PIN_NUM_DATA12,
            LCD_I80_PIN_NUM_DATA13,
            LCD_I80_PIN_NUM_DATA14,
            LCD_I80_PIN_NUM_DATA15,
#endif
        },
        .bus_width = CONFIG_LCD_I80_BUS_WIDTH,
        .max_transfer_bytes = lcd->height * lcd->display_row * sizeof(uint16_t),
        .dma_burst_size = LCD_DMA_BURST_SIZE,
    };
    ESP_ERROR_CHECK(esp_lcd_new_i80_bus(&bus_config, &i80_bus));

    esp_lcd_panel_io_i80_config_t io_config = {
        .cs_gpio_num = LCD_I80_PIN_NUM_CS,
        .pclk_hz = LCD_PIXEL_CLOCK_HZ,
        .trans_queue_depth = 10,
        .dc_levels = {
            .dc_idle_level = 0,
            .dc_cmd_level = 0,
            .dc_dummy_level = 0,
            .dc_data_level = 1,
        },
        .flags = {
            .swap_color_bytes = 0, // Swap can be done in LvGL (default) or DMA
        },
        .on_color_trans_done = lcd_flush_ready,
        .user_ctx = lcd,
        .lcd_cmd_bits = LCD_CMD_BITS,
        .lcd_param_bits = LCD_PARAM_BITS,
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_i80(i80_bus, &io_config, &lcd->io_handle));
}

static void lcd_i80_panel_init(lcd_i80_t *lcd)
{
    esp_lcd_panel_handle_t panel_handle = NULL;
#if CONFIG_LCD_I80_CONTROLLER_ST7789
    ESP_LOGI(TAG, "Install LCD driver of st7789");
    esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = LCD_I80_PIN_NUM_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,
        .bits_per_pixel = 16,
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_st7789(lcd->io_handle, &panel_config, &panel_handle));

    esp_lcd_panel_reset(panel_handle);
    esp_lcd_panel_init(panel_handle);
    // Set inversion, x/y coordinate order, x/y mirror according to your LCD module spec
    // the gap is LCD panel specific, even panels with the same driver IC, can have different gap value
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0x36, (uint8_t[]){0x60}, 1); // 0xA0 Memory Access Control
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0xB1, (uint8_t[]){0}, 1); // Frame Rate Control

    esp_lcd_panel_invert_color(panel_handle, true);
    esp_lcd_panel_set_gap(panel_handle, 0, 0);
#elif CONFIG_LCD_I80_CONTROLLER_NT35510
    ESP_LOGI(TAG, "Install LCD driver of nt35510");
    esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = LCD_I80_PIN_NUM_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_BGR,
        .bits_per_pixel = 16,
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_nt35510(lcd->io_handle, &panel_config, &panel_handle));

    esp_lcd_panel_reset(panel_handle);
    esp_lcd_panel_init(panel_handle);
    // Set inversion, x/y coordinate order, x/y mirror according to your LCD module spec
    // the gap is LCD panel specific, even panels with the same driver IC, can have different gap value
    esp_lcd_panel_swap_xy(panel_handle, true);
    esp_lcd_panel_mirror(panel_handle, true, false);
#elif CONFIG_LCD_I80_CONTROLLER_ILI9341
    // ILI9341 is NOT a distinct driver, but a special case of ST7789
    // (essential registers are identical). A few lines further down in this code,
    // it's shown how to issue additional device-specific commands.
    ESP_LOGI(TAG, "Install LCD driver of ili9341 (st7789 compatible)");
    esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = LCD_I80_PIN_NUM_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_BGR,
        .bits_per_pixel = 16,
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_st7789(lcd->io_handle, &panel_config, &panel_handle));

    esp_lcd_panel_reset(panel_handle);
    esp_lcd_panel_init(panel_handle);
    // Set inversion, x/y coordinate order, x/y mirror according to your LCD module spec
    // the gap is LCD panel specific, even panels with the same driver IC, can have different gap value
    esp_lcd_panel_swap_xy(panel_handle, true);
    esp_lcd_panel_invert_color(panel_handle, false);
    // ILI9341 is very similar to ST7789 and shares the same driver.
    // Anything unconventional (such as this custom gamma table) can
    // be issued here in user code and need not modify the driver.
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0x36, (uint8_t[]){0x28}, 1); // Memory Access Control
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0xB1, (uint8_t[]){0}, 1); // Frame Rate Control
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0xB6, (uint8_t[]){0}, 1); // Display Function Control
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0xF2, (uint8_t[]){0}, 1); // 3Gamma function disable
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0x26, (uint8_t[]){1}, 1); // Gamma curve 1 selected
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0xE0, (uint8_t[]){        // Set positive gamma
                                                                0x0F, 0x31, 0x2B, 0x0C, 0x0E, 0x08, 0x4E, 0xF1, 0x37, 0x07, 0x10, 0x03, 0x0E, 0x09, 0x00},
                              15);
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0xE1, (uint8_t[]){// Set negative gamma
                                                                0x00, 0x0E, 0x14, 0x03, 0x11, 0x07, 0x31, 0xC1, 0x48, 0x08, 0x0F, 0x0C, 0x31, 0x36, 0x0F},
                              15);
#endif
    lcd->panel_handle = panel_handle;
}

void lcd_i80_init(lcd_i80_t *lcd)
{
#if LCD_I80_PIN_NUM_BK_LIGHT >= 0
    ESP_LOGI(TAG, "Turn off LCD backlight");
    gpio_config_t bk_gpio_config = {
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pin_bit_mask = 1ULL << LCD_I80_PIN_NUM_BK_LIGHT};
    ESP_ERROR_CHECK(gpio_config(&bk_gpio_config));
    gpio_set_level(LCD_I80_PIN_NUM_BK_LIGHT, 0);
#endif // LCD_I80_PIN_NUM_BK_LIGHT >= 0

    if(lcd->display_row <= 0)
        lcd->display_row = 1;

    lcd_i80_bus_init(lcd);
    lcd_i80_panel_init(lcd);

    ESP_ERROR_CHECK(esp_lcd_panel_disp_sleep(lcd->panel_handle, false)); //exit sleep mode

    // Stub: user can flush pre-defined pattern to the screen before we turn on the screen or backlight

    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(lcd->panel_handle, true));

#if (CONFIG_LCD_I80_CONTROL_BACK_LIGHT_USED)
    _ledc_timer_config();
    _ledc_channel_config();
#endif
}

void lcd_i80_address_set(lcd_i80_t *lcd, unsigned int x1, unsigned int y1, unsigned int x2, unsigned int y2)
{
#if (CONFIG_LCD_I80_CONTROLLER_ST7789 || CONFIG_LCD_I80_CONTROLLER_ILI9341)
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0x2A, (uint8_t[]){
                                                        (x1 >> 8) & 0xFF,
                                                        x1 & 0xFF,
                                                        (x2 >> 8) & 0xFF,
                                                        x2 & 0xFF,
                                                    },
                              4);
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0x2B, (uint8_t[]){
                                                        (y1 >> 8) & 0xFF,
                                                        y1 & 0xFF,
                                                        (y2 >> 8) & 0xFF,
                                                        y2 & 0xFF,
                                                    },
                              4);
    esp_lcd_panel_io_tx_param(lcd->io_handle, 0x2C, NULL, 0);
#elif CONFIG_LCD_I80_CONTROLLER_NT35510

#endif
}

void lcd_i80_clear(lcd_i80_t *lcd, uint16_t color)
{
    const int lines = 40; // 行数
    int i, j, row, col;
    uint16_t *buff = NULL;

    col = lcd->width > lcd->height ? lcd->width : lcd->height; //列数
    row = (col == lcd->width) ? lcd->height : lcd->width; //行数

    buff = (uint16_t *)malloc(col * sizeof(uint16_t) * lines);
    assert(buff);

    for (i = 0; i < row; i += lines)
    {
        for (j = 0; j < col * lines; j++)
        {
            buff[j] = color;
        }

        lcd_i80_address_set(lcd, 0, i, col - 1, i + lines - 1);
        esp_lcd_panel_io_tx_color(lcd->io_handle, -1, buff, col * sizeof(uint16_t) * lines);
    }

    free(buff);
    buff = NULL;
}

void lcd_i80_draw_point(lcd_i80_t *lcd, int x, int y, uint16_t color)
{
    lcd_i80_address_set(lcd, x, y, x, y);
    esp_lcd_panel_io_tx_color(lcd->io_handle, -1, &color, 2);
}

// 画线
// x1,y1:起点坐标
// x2,y2:终点坐标
void lcd_i80_draw_line(lcd_i80_t *lcd, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    int16_t n, dx, dy, sgndx, sgndy, dxabs, dyabs, x, y, drawx, drawy;

    dx = x2 - x1;
    dy = y2 - y1;
    dxabs = (dx > 0) ? dx : -dx;
    dyabs = (dy > 0) ? dy : -dy;
    sgndx = (dx > 0) ? 1 : -1;
    sgndy = (dy > 0) ? 1 : -1;
    x = dyabs >> 1;
    y = dxabs >> 1;
    drawx = x1;
    drawy = y1;

    lcd_i80_draw_point(lcd, drawx, drawy, color);

    if (dxabs >= dyabs)
    {
        for (n = 0; n < dxabs; n++)
        {
            y += dyabs;
            if (y >= dxabs)
            {
                y -= dxabs;
                drawy += sgndy;
            }
            drawx += sgndx;
            lcd_i80_draw_point(lcd, drawx, drawy, color);
        }
    }
    else
    {
        for (n = 0; n < dyabs; n++)
        {
            x += dxabs;
            if (x >= dyabs)
            {
                x -= dyabs;
                drawx += sgndx;
            }
            drawy += sgndy;
            lcd_i80_draw_point(lcd, drawx, drawy, color);
        }
    }
}

void lcd_i80_push_image(lcd_i80_t *lcd, int x_start, int y_start, int x_end, int y_end, const void *color_data)
{
    esp_lcd_panel_draw_bitmap(lcd->panel_handle, x_start, y_start, x_end, y_end, color_data);
}

void *lcd_i80_alloc_drawbuff(lcd_i80_t *lcd, size_t buff_size)
{
    uint32_t draw_buf_alloc_caps = 0;
#if CONFIG_LCD_I80_COLOR_IN_PSRAM
    draw_buf_alloc_caps |= MALLOC_CAP_SPIRAM;
#endif

    return esp_lcd_i80_alloc_draw_buffer(lcd->io_handle, buff_size, draw_buf_alloc_caps);
}

void lcd_i80_backlight_set(lcd_i80_t *lcd, uint8_t val)
{
    if (val > 100)
        val = 100;
#if (CONFIG_LCD_I80_CONTROL_BACK_LIGHT_USED)
    _ledc_pwm_duty_set(val);
#else
    if (val)
        gpio_set_level(LCD_I80_PIN_NUM_BK_LIGHT, 1);
    else
        gpio_set_level(LCD_I80_PIN_NUM_BK_LIGHT, 0);
#endif

    lcd->bkl = val;
}

#if (CONFIG_LCD_I80_CONTROL_BACK_LIGHT_USED)
static void _ledc_pwm_duty_set(uint8_t duty_p)
{
    uint16_t Duty, MaxD;

    MaxD = (1 << (int)ledc_tConfig.duty_resolution) - 1;

    if (duty_p >= 100)
        Duty = MaxD;
    else
    {
        Duty = duty_p * (MaxD / (float)100);
    }
    ledc_cConfig.duty = Duty;
    ledc_channel_config(&ledc_cConfig);
}

static void _ledc_channel_config(void)
{
    ledc_cConfig.gpio_num = LCD_I80_PIN_NUM_BK_LIGHT;
    ledc_cConfig.speed_mode = LEDC_LOW_SPEED_MODE;
    ledc_cConfig.channel = LEDC_CHANNEL_0;
    ledc_cConfig.intr_type = LEDC_INTR_DISABLE;
    ledc_cConfig.timer_sel = LEDC_TIMER_0;
    ledc_cConfig.duty = 0;
    ledc_cConfig.hpoint = 0;
    ledc_channel_config(&ledc_cConfig);
}

static void _ledc_timer_config(void)
{
    ledc_tConfig.speed_mode = LEDC_LOW_SPEED_MODE;
    ledc_tConfig.duty_resolution = LEDC_TIMER_8_BIT;
    // ledc_tConfig.bit_num=LEDC_TIMER_8_BIT;
    ledc_tConfig.timer_num = LEDC_TIMER_0;
    ledc_tConfig.freq_hz = 1000;
    ledc_tConfig.clk_cfg = LEDC_AUTO_CLK;
    ledc_timer_config(&ledc_tConfig);
}
#endif

uint8_t lcd_i80_get_backlight(lcd_i80_t *lcd)
{
    return lcd->bkl;
}
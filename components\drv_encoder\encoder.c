#include <stdio.h>
#include "encoder.h"
#include "board_pins_user.h"

#define TAG "encoder"

#define ENCODER_PCNT_HIGH_LIMIT 1000
#define ENCODER_PCNT_LOW_LIMIT  -1000

#ifndef DEF_ENCODER_ECA_GPIO
#define ENCODER_PIN_NUM_EC_A CONFIG_ENCODER_PIN_NUM_EC_A
#else
#define ENCODER_PIN_NUM_EC_A DEF_ENCODER_ECA_GPIO
#endif

#ifndef DEF_ENCODER_ECB_GPIO
#define ENCODER_PIN_NUM_EC_B CONFIG_ENCODER_PIN_NUM_EC_B
#else
#define ENCODER_PIN_NUM_EC_B DEF_ENCODER_ECB_GPIO
#endif

#ifndef DEF_ENCODER_ECK_GPIO
#define ENCODER_PIN_NUM_EC_K CONFIG_ENCODER_PIN_NUM_EC_K
#else
#define ENCODER_PIN_NUM_EC_K DEF_ENCODER_ECK_GPIO
#endif


#if (DEF_ENCODER_ECA_GPIO >= 0 && DEF_ENCODER_ECB_GPIO >= 0 && DEF_ENCODER_ECK_GPIO >= 0)

encoder_t encoder;

void drv_encoder_init()
{
    encoder.high_limit = ENCODER_PCNT_HIGH_LIMIT >> 2;
    encoder.low_limit = ENCODER_PCNT_LOW_LIMIT >> 2;

#if ENCODER_PIN_NUM_EC_K >= 0
    gpio_config_t eck_gpio_config = {
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = 1ULL << ENCODER_PIN_NUM_EC_K,
        .pull_up_en = GPIO_PULLUP_ENABLE};
    ESP_ERROR_CHECK(gpio_config(&eck_gpio_config));
#endif // ENCODER_PIN_NUM_EC_K >= 0

    ESP_LOGI(TAG, "install pcnt unit");
    pcnt_unit_config_t unit_config = {
        .high_limit = ENCODER_PCNT_HIGH_LIMIT,
        .low_limit = ENCODER_PCNT_LOW_LIMIT,
    };
    ESP_ERROR_CHECK(pcnt_new_unit(&unit_config, &encoder.pcnt_unit));

    ESP_LOGI(TAG, "set glitch filter");
    pcnt_glitch_filter_config_t filter_config = {
        .max_glitch_ns = 1000,
    };
    ESP_ERROR_CHECK(pcnt_unit_set_glitch_filter(encoder.pcnt_unit, &filter_config));

    ESP_LOGI(TAG, "install pcnt channels");
    pcnt_chan_config_t chan_a_config = {
        .edge_gpio_num = ENCODER_PIN_NUM_EC_A,
        .level_gpio_num = ENCODER_PIN_NUM_EC_B,
    };
    pcnt_channel_handle_t pcnt_chan_a = NULL;
    ESP_ERROR_CHECK(pcnt_new_channel(encoder.pcnt_unit, &chan_a_config, &pcnt_chan_a));
    pcnt_chan_config_t chan_b_config = {
        .edge_gpio_num = ENCODER_PIN_NUM_EC_B,
        .level_gpio_num = ENCODER_PIN_NUM_EC_A,
    };
    pcnt_channel_handle_t pcnt_chan_b = NULL;
    ESP_ERROR_CHECK(pcnt_new_channel(encoder.pcnt_unit, &chan_b_config, &pcnt_chan_b));

    ESP_LOGI(TAG, "set edge and level actions for pcnt channels");
    ESP_ERROR_CHECK(pcnt_channel_set_edge_action(pcnt_chan_a, PCNT_CHANNEL_EDGE_ACTION_DECREASE, PCNT_CHANNEL_EDGE_ACTION_INCREASE));
    ESP_ERROR_CHECK(pcnt_channel_set_level_action(pcnt_chan_a, PCNT_CHANNEL_LEVEL_ACTION_KEEP, PCNT_CHANNEL_LEVEL_ACTION_INVERSE));
    ESP_ERROR_CHECK(pcnt_channel_set_edge_action(pcnt_chan_b, PCNT_CHANNEL_EDGE_ACTION_INCREASE, PCNT_CHANNEL_EDGE_ACTION_DECREASE));
    ESP_ERROR_CHECK(pcnt_channel_set_level_action(pcnt_chan_b, PCNT_CHANNEL_LEVEL_ACTION_KEEP, PCNT_CHANNEL_LEVEL_ACTION_INVERSE));

    ESP_LOGI(TAG, "enable pcnt unit");
    ESP_ERROR_CHECK(pcnt_unit_enable(encoder.pcnt_unit));
    ESP_LOGI(TAG, "clear pcnt unit");
    ESP_ERROR_CHECK(pcnt_unit_clear_count(encoder.pcnt_unit));
    ESP_LOGI(TAG, "start pcnt unit");
    ESP_ERROR_CHECK(pcnt_unit_start(encoder.pcnt_unit));
}

encoder_t * drv_encoder_read()
{
    ESP_ERROR_CHECK(pcnt_unit_get_count(encoder.pcnt_unit, &encoder.pulse_count));
    encoder.pulse_count >>= 2;
    encoder.key_state =  gpio_get_level(ENCODER_PIN_NUM_EC_K) == 0 ? ENCODER_KEY_STATE_PR : ENCODER_KEY_STATE_REL; //低电平按下

    // ESP_LOGI(TAG, "pcnt,key == > %d,%d", encoder.pulse_count, encoder.key_state);

    return &encoder;
}

#endif


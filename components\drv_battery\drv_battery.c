#include <stdio.h>
#include "drv_battery.h"

#include <string.h>

#include "audio_mem.h"
#include "esp_log.h"
#include "driver/adc.h"
#include "esp_adc_cal.h"
#include "battery_service.h"
#include "esp_idf_version.h"
#include "board_pins_user.h"
#include "math_util.h"

#if (DEF_BATTERY_CHARG_GPIO < 0)
#include "drv_pca9557.h"
#endif

#define TAG "battery"

#define ADC_SAMPLES_NUM (10)

#if (ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0))
#define ADC_ATTEN_11db ADC_ATTEN_DB_11
#define ADC_WIDTH_12Bit ADC_BITWIDTH_12
#define ADC_WIDTH_13Bit ADC_BITWIDTH_13
#endif

// Static
static bool adc_init(void *user_data);
static bool adc_deinit(void *user_data);
static int vol_read(void *user_data);

struct
{
    xQueueHandle queue;
    int count;
} bat_data;

typedef struct
{
    int unit;    /*!< ADC unit, see `adc_unit_t` */
    int chan;    /*!< ADC channel, see `adc_channel_t` */
    int width;   /*!< ADC width, see `adc_channel_t` */
    int atten;   /*!< ADC atten, see `adc_atten_t` */
    int v_ref;   /*!< default vref` */
    float ratio; /*!< divider ratio` */
} vol_adc_param_t;

vol_adc_param_t adc_cfg = {
    .unit = ADC_UNIT_1,
    .chan = ADC1_CHANNEL_0,
    .ratio = 2.01,
#ifdef CONFIG_IDF_TARGET_ESP32S2
    .width = ADC_WIDTH_BIT_13,
#else
    .width = ADC_WIDTH_BIT_12,
#endif
    .atten = ADC_ATTEN_11db,
#ifdef CONFIG_IDF_TARGET_ESP32
    .v_ref = 1100,
#else
    .v_ref = 0,
#endif
};

vol_monitor_param_t vol_monitor_cfg = {
    .init = adc_init,
    .deinit = adc_deinit,
    .vol_get = vol_read,
    .read_freq = 2,
    .report_freq = 2,
    .vol_full_threshold = CONFIG_VOLTAGE_OF_BATTERY_FULL,
    .vol_low_threshold = CONFIG_VOLTAGE_OF_BATTERY_LOW,
};

battery_service_config_t bat_serv_config = BATTERY_SERVICE_DEFAULT_CONFIG();
periph_service_handle_t battery_service;
battery_info_t battery_info;

static bool adc_init(void *user_data)
{
    // vol_adc_param_t *adc_cfg = (vol_adc_param_t *)user_data;
    // adc1_config_width(adc_cfg->width);
    // adc1_config_channel_atten(adc_cfg->chan, adc_cfg->atten);
    return true;
}

static bool adc_deinit(void *user_data)
{
    return true;
}

static int vol_read(void *user_data)
{
    vol_adc_param_t *adc_cfg = (vol_adc_param_t *)user_data;

    uint32_t adc_data = 0;
    uint32_t data[ADC_SAMPLES_NUM] = {0};
    uint32_t sum = 0;
    int tmp = 0;

    esp_adc_cal_characteristics_t characteristics;
    esp_adc_cal_characterize(adc_cfg->unit, adc_cfg->atten, adc_cfg->width, adc_cfg->v_ref, &characteristics);

    for (int i = 0; i < ADC_SAMPLES_NUM; ++i)
    {
        xQueueReceive(bat_data.queue, &adc_data, 10);
        data[i] = esp_adc_cal_raw_to_voltage(adc_data, &characteristics);

        // esp_adc_cal_get_voltage(adc_cfg->chan, &characteristics, &data[i]);
    }

    // 重置队列计数
    bat_data.count = 0;

    for (int j = 0; j < ADC_SAMPLES_NUM - 1; j++)
    {
        for (int i = 0; i < ADC_SAMPLES_NUM - j - 1; i++)
        {
            if (data[i] > data[i + 1])
            {
                tmp = data[i];
                data[i] = data[i + 1];
                data[i + 1] = tmp;
            }
        }
    }
    for (int num = 1; num < ADC_SAMPLES_NUM - 1; num++)
        sum += data[num];
    return (int)((sum / (ADC_SAMPLES_NUM - 2)) * adc_cfg->ratio);
}

static esp_err_t battery_service_cb(periph_service_handle_t handle, periph_service_event_t *evt, void *ctx)
{
    if (evt->type == BAT_SERV_EVENT_VOL_REPORT)
    {
        int voltage = (int)evt->data;

        battery_info.value = voltage;
        battery_info.pct = math_map(battery_info.value, CONFIG_VOLTAGE_OF_BATTERY_LOW, CONFIG_VOLTAGE_OF_BATTERY_FULL, 0, 100);

#if (DEF_BATTERY_CHARG_GPIO >= 0)
        battery_info.state_chrg = gpio_get_level(DEF_BATTERY_CHARG_GPIO) ? BATTERY_STATE_NOCHRG : BATTERY_STATE_CHRG;
#else
        battery_info.state_chrg = drv_pca9557_read(EXTEND_PIN_4_BAT_CHRG) ? BATTERY_STATE_CHRG : BATTERY_STATE_NOCHRG;
#endif

        if (battery_info.pct > 0 && battery_info.pct <= 25)
        {
            battery_info.state = BATTERY_STATE_25;
        }
        else if (battery_info.pct > 25 && battery_info.pct <= 50)
        {
            battery_info.state = BATTERY_STATE_50;
        }
        else if (battery_info.pct > 50 && battery_info.pct <= 75)
        {
            battery_info.state = BATTERY_STATE_75;
        }
        else if (battery_info.pct > 75 && battery_info.pct <= 100)
        {
            battery_info.state = BATTERY_STATE_100;
        }
        else
        {
            battery_info.state = BATTERY_STATE_0;
        }

        // ESP_LOGI(TAG, "got voltage %d, %d, %d", voltage, battery_info.pct, battery_info.state_chrg);
    }
    else if (evt->type == BAT_SERV_EVENT_BAT_FULL)
    {
        int voltage = (int)evt->data;
        ESP_LOGW(TAG, "battery full %d", voltage);
    }
    else if (evt->type == BAT_SERV_EVENT_BAT_LOW)
    {
        int voltage = (int)evt->data;
    }
    else
    {
        ESP_LOGW(TAG, "unrecognized event %d", evt->type);
    }
    return ESP_OK;
}

void drv_battery_init()
{
#if (DEF_BATTERY_CHARG_GPIO >= 0)
    gpio_config_t bat_charg_gpio_config = {
        .mode = GPIO_MODE_DEF_INPUT,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pin_bit_mask = 1ULL << DEF_BATTERY_CHARG_GPIO};
    ESP_ERROR_CHECK(gpio_config(&bat_charg_gpio_config));
#else
    drv_pca9557_pinmode(EXTEND_PIN_3_BAT_CTRL, EXTEND_OUTPUT);
    drv_pca9557_pinmode(EXTEND_PIN_4_BAT_CHRG, EXTEND_INPUT);

    drv_pca9557_write(EXTEND_PIN_3_BAT_CTRL, EXTEND_HIGH);
#endif

    bat_data.queue = xQueueCreate(ADC_SAMPLES_NUM, sizeof(uint16_t));

    vol_monitor_cfg.user_data = audio_malloc(sizeof(vol_adc_param_t));
    AUDIO_MEM_CHECK(TAG, vol_monitor_cfg.user_data, return);
    memcpy(vol_monitor_cfg.user_data, &adc_cfg, sizeof(vol_adc_param_t));

    ESP_LOGI(TAG, "[1.0] create battery service");
    bat_serv_config.task_prio = 4;
    bat_serv_config.task_core = 0;
    bat_serv_config.evt_cb = battery_service_cb;
    bat_serv_config.vol_monitor = vol_monitor_create(&vol_monitor_cfg);
    battery_service = battery_service_create(&bat_serv_config);

    ESP_LOGI(TAG, "[1.1] start battery service");
    if (periph_service_start(battery_service) != ESP_OK)
    {
        drv_battery_deinit();
        return;
    }

    drv_battery_report_sw(true);
}

void drv_battery_deinit()
{
    if (bat_data.queue)
    {
        vQueueDelete(bat_data.queue);
    }

    ESP_LOGI(TAG, "[2.1] destroy battery service");
    periph_service_destroy(battery_service);
    vol_monitor_destroy(bat_serv_config.vol_monitor);
    free(vol_monitor_cfg.user_data);

#if (DEF_BATTERY_CHARG_GPIO >= 0)

#else
    drv_pca9557_write(EXTEND_PIN_3_BAT_CTRL, EXTEND_LOW);
#endif
}

void drv_battery_send_data(uint16_t *data)
{
    BaseType_t xReturn = pdPASS;
    if (bat_data.queue == NULL)
        return;

    if (bat_data.count < ADC_SAMPLES_NUM)
    {
        xReturn = xQueueSend(bat_data.queue, data, 0);
        bat_data.count++;
    }
}

void drv_battery_report_sw(bool state)
{
    battery_service_vol_report_switch(battery_service, state);
    if (state)
    {
        ESP_LOGI(TAG, "[1.2] start battery voltage report");
    }
    else
    {
        ESP_LOGI(TAG, "[2.0] stop battery voltage report");
    }
}

void drv_battery_report_freq(int freq)
{
    ESP_LOGI(TAG, "[1.3] change battery voltage report freqency");
    battery_service_set_vol_report_freq(battery_service, freq);
}
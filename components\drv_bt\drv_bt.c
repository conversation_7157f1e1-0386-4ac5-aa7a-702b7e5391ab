#include <stdio.h>
#include "drv_bt.h"
#include "driver/gpio.h"
#include "board_pins_user.h"

#if (DEF_SPEAK_SW_GPIO < 0)
#include "drv_pca9557.h"
#endif

void drv_bt_init(void)
{
#if (DEF_SPEAK_SW_GPIO >= 0)
    gpio_config_t ble_gpio_config = {
        .mode = GPIO_MODE_INPUT_OUTPUT,
        .pull_down_en = GPIO_PULLDOWN_ENABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pin_bit_mask = 1ULL << DEF_SPEAK_SW_GPIO};
    ESP_ERROR_CHECK(gpio_config(&ble_gpio_config));
    gpio_set_level(DEF_SPEAK_SW_GPIO, 0);
#else
    drv_pca9557_pinmode(EXTEND_PIN_6_BLE_CTRL, EXTEND_OUTPUT);
    drv_pca9557_write(EXTEND_PIN_6_BLE_CTRL, EXTEND_LOW);
#endif
}

void drv_bt_deinit(void)
{
}

void drv_bt_enable(void)
{
#if (DEF_SPEAK_SW_GPIO >= 0)
    gpio_set_level(DEF_SPEAK_SW_GPIO, 1);
#else
    drv_pca9557_write(EXTEND_PIN_6_BLE_CTRL, EXTEND_HIGH);
#endif
}

void drv_bt_disable(void)
{
#if (DEF_SPEAK_SW_GPIO >= 0)
    gpio_set_level(DEF_SPEAK_SW_GPIO, 0);
#else
    drv_pca9557_write(EXTEND_PIN_6_BLE_CTRL, EXTEND_LOW);
#endif
}

bool drv_bt_isenable(void)
{
#if (DEF_SPEAK_SW_GPIO >= 0)
    return gpio_get_level(DEF_SPEAK_SW_GPIO);
#else
    return drv_pca9557_read_output(EXTEND_PIN_6_BLE_CTRL);
#endif
}

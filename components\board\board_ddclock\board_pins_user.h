#ifndef _AUDIO_BOARD_PINS_USER_H_
#define _AUDIO_BOARD_PINS_USER_H_

// I2C
#define DEF_I2C_SCL_GPIO 0
#define DEF_I2C_SDA_GPIO 42

// LCD
#define DEF_LCD_BLK_GPIO 15
#define DEF_LCD_CS_GPIO 45
#define DEF_LCD_RST_GPIO -1  // 46
#define DEF_LCD_DC_GPIO 48   // RS
#define DEF_LCD_PCLK_GPIO 47 // WR
#define DEF_LCD_DB0_GPIO 46
#define DEF_LCD_DB1_GPIO 9
#define DEF_LCD_DB2_GPIO 10
#define DEF_LCD_DB3_GPIO 11
#define DEF_LCD_DB4_GPIO 12
#define DEF_LCD_DB5_GPIO 13
#define DEF_LCD_DB6_GPIO 14
#define DEF_LCD_DB7_GPIO 21
#define DEF_LCD_DB8_GPIO -1
#define DEF_LCD_DB9_GPIO -1
#define DEF_LCD_DB10_GPIO -1
#define DEF_LCD_DB11_GPIO -1
#define DEF_LCD_DB12_GPIO -1
#define DEF_LCD_DB13_GPIO -1
#define DEF_LCD_DB14_GPIO -1
#define DEF_LCD_DB15_GPIO -1

// BATTERY
#define DEF_BATTERY_ADC_GPIO 1
#define DEF_BATTERY_CHARG_GPIO -1

// ENCODER
#define DEF_ENCODER_ECA_GPIO -1
#define DEF_ENCODER_ECB_GPIO -1
#define DEF_ENCODER_ECK_GPIO -1

// KEY
#define DEF_KEY_A_GPIO 41
#define DEF_KEY_B_GPIO 40
#define DEF_KEY_C_GPIO 39

// SD CARD
#define DEF_SD_INTR_GPIO 38
#define DEF_SD_CS_GPIO -1 // D3
#define DEF_SD_D0_GPIO 4 // MISO
#define DEF_SD_CMD_GPIO 6 // MOSI
#define DEF_SD_CLK_GPIO 5

// I2S
#define DEF_I2S_DOUT_GPIO 16 // DIN
#define DEF_I2S_BCLK_GPIO 17 // CLK
#define DEF_I2S_WS_GPIO 18   // LRCLK

// SPEAK SWITCH
#define DEF_SPEAK_SW_GPIO -1

// BEEP
#define DEF_BEEP_GPIO -1

// AMIC
#define DEF_AMIC_GPIO 2

// WAKE_UP
#define DEF_WAKEUP_GPIO 7

#endif
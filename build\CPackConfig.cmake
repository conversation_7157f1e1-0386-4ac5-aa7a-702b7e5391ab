# This file will be configured to contain variables for CPack. These variables
# should be set in the CMake list file of the project before CPack module is
# included. The list of available CPACK_xxx variables and their associated
# documentation may be obtained using
#  cpack --help-variable-list
#
# Some variables are common to all generators (e.g. CPACK_PACKAGE_NAME)
# and some are specific to a generator
# (e.g. CPACK_NSIS_EXTRA_INSTALL_COMMANDS). The generator specific variables
# usually begin with CPACK_<GENNAME>_xxxx.


set(CPACK_BINARY_7Z "OFF")
set(CPACK_BINARY_IFW "OFF")
set(CPACK_BINARY_INNOSETUP "OFF")
set(CPACK_BINARY_NSIS "ON")
set(CPACK_BINARY_NUGET "OFF")
set(CPACK_BINARY_WIX "OFF")
set(CPACK_BINARY_ZIP "OFF")
set(CPACK_BUILD_SOURCE_DIRS "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build")
set(CPAC<PERSON>_CMAKE_GENERATOR "Ninja")
set(CPACK_COMPONENT_UNSPECIFIED_HIDDEN "TRUE")
set(CPACK_COMPONENT_UNSPECIFIED_REQUIRED "TRUE")
set(CPACK_DEFAULT_PACKAGE_DESCRIPTION_FILE "E:/Espressif/533/tools/tools/cmake/3.30.2/share/cmake-3.30/Templates/CPack.GenericDescription.txt")
set(CPACK_DEFAULT_PACKAGE_DESCRIPTION_SUMMARY "DDClock built using CMake")
set(CPACK_GENERATOR "NSIS")
set(CPACK_INNOSETUP_ARCHITECTURE "x86")
set(CPACK_INSTALL_CMAKE_PROJECTS "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build;DDClock;ALL;/")
set(CPACK_INSTALL_PREFIX "C:/Program Files (x86)/DDClock")
set(CPACK_MODULE_PATH "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/managed_components/espressif__cmake_utilities;E:/Espressif/533/v5.3.3/esp-idf/tools/cmake;E:/Espressif/533/v5.3.3/esp-idf/tools/cmake/third_party;C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/components/esp_ogg/ogg/cmake")
set(CPACK_NSIS_DISPLAY_NAME "DDClock 1.3.5")
set(CPACK_NSIS_INSTALLER_ICON_CODE "")
set(CPACK_NSIS_INSTALLER_MUI_ICON_CODE "")
set(CPACK_NSIS_INSTALL_ROOT "$PROGRAMFILES")
set(CPACK_NSIS_PACKAGE_NAME "DDClock 1.3.5")
set(CPACK_NSIS_UNINSTALL_NAME "Uninstall")
set(CPACK_OBJCOPY_EXECUTABLE "E:/Espressif/533/tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-objcopy.exe")
set(CPACK_OBJDUMP_EXECUTABLE "E:/Espressif/533/tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-objdump.exe")
set(CPACK_OUTPUT_CONFIG_FILE "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/CPackConfig.cmake")
set(CPACK_PACKAGE_DEFAULT_LOCATION "/")
set(CPACK_PACKAGE_DESCRIPTION_FILE "E:/Espressif/533/tools/tools/cmake/3.30.2/share/cmake-3.30/Templates/CPack.GenericDescription.txt")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "DDClock built using CMake")
set(CPACK_PACKAGE_FILE_NAME "DDClock-1.3.5-Generic")
set(CPACK_PACKAGE_INSTALL_DIRECTORY "DDClock 1.3.5")
set(CPACK_PACKAGE_INSTALL_REGISTRY_KEY "DDClock 1.3.5")
set(CPACK_PACKAGE_NAME "DDClock")
set(CPACK_PACKAGE_RELOCATABLE "true")
set(CPACK_PACKAGE_VENDOR "Humanity")
set(CPACK_PACKAGE_VERSION "1.3.5")
set(CPACK_PACKAGE_VERSION_MAJOR "3")
set(CPACK_PACKAGE_VERSION_MINOR "6")
set(CPACK_PACKAGE_VERSION_PATCH "2")
set(CPACK_READELF_EXECUTABLE "E:/Espressif/533/tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-readelf.exe")
set(CPACK_RESOURCE_FILE_LICENSE "E:/Espressif/533/tools/tools/cmake/3.30.2/share/cmake-3.30/Templates/CPack.GenericLicense.txt")
set(CPACK_RESOURCE_FILE_README "E:/Espressif/533/tools/tools/cmake/3.30.2/share/cmake-3.30/Templates/CPack.GenericDescription.txt")
set(CPACK_RESOURCE_FILE_WELCOME "E:/Espressif/533/tools/tools/cmake/3.30.2/share/cmake-3.30/Templates/CPack.GenericWelcome.txt")
set(CPACK_SET_DESTDIR "OFF")
set(CPACK_SOURCE_7Z "ON")
set(CPACK_SOURCE_GENERATOR "7Z;ZIP")
set(CPACK_SOURCE_OUTPUT_CONFIG_FILE "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/CPackSourceConfig.cmake")
set(CPACK_SOURCE_ZIP "ON")
set(CPACK_SYSTEM_NAME "Generic")
set(CPACK_THREADS "1")
set(CPACK_TOPLEVEL_TAG "Generic")
set(CPACK_WIX_SIZEOF_VOID_P "4")

if(NOT CPACK_PROPERTIES_FILE)
  set(CPACK_PROPERTIES_FILE "C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/build/CPackProperties.cmake")
endif()

if(EXISTS ${CPACK_PROPERTIES_FILE})
  include(${CPACK_PROPERTIES_FILE})
endif()

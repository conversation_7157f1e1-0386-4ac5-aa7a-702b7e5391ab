menu "LCD_I80 configuration"
    config LCD_I80_COLOR_IN_PSRAM
        bool "Allocate color data from PSRAM"
        depends on SOC_PSRAM_DMA_CAPABLE
        default y
        help
            Enable this option if you want to allocate the LVGL draw buffer from PSRAM.

    config LCD_I80_PIXEL_CLOCK_HZ
        int "Pixel clock frequency (Hz)"
        default 2000000 if LCD_I80_COLOR_IN_PSRAM && IDF_TARGET_ESP32S3
        default 10000000
        help
            Set the pixel clock frequency in Hz.

    choice LCD_I80_CONTROLLER_MODEL
        prompt "i80 LCD controller model"
        default LCD_I80_CONTROLLER_ST7789
        help
            Select LCD controller model

        config LCD_I80_CONTROLLER_ST7789
            bool "ST7789"

        config LCD_I80_CONTROLLER_NT35510
            bool "NT35510"

        config LCD_I80_CONTROLLER_ILI9341
            bool "ILI9341"
    endchoice

    if LCD_I80_CONTROLLER_NT35510
        choice LCD_NT35510_DATA_WIDTH
            prompt "NT35510 Data Width"
            default LCD_NT35510_DATA_WIDTH_8
            help
                Select NT35510 Data Width (8 or 16), a.k.a, the number of data lines.

            config LCD_NT35510_DATA_WIDTH_8
                bool "8"

            config LCD_NT35510_DATA_WIDTH_16
                bool "16"
        endchoice
    endif

    config LCD_I80_CONTROL_BACK_LIGHT_USED
        bool "i80 LCD Control Back Light"
        default y
    
    menu "LCD_I80 Pins configuration"
        if !LCD_I80_CONTROLLER_NT35510
            config LCD_I80_BUS_WIDTH
                int "i80 LCD Bus width"
                default 16 if LCD_NT35510_DATA_WIDTH_16
                default 8
                help 
                    Data Width (8 or 16), a.k.a, the number of data lines.
        endif

        config LCD_I80_PIN_NUM_BK_LIGHT
            int "i80 LCD Pin BLK"
            default 0 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_RST
            int "i80 LCD Pin RST"
            default 2 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DC
            int "i80 LCD Pin DC(RS)"
            default 4 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_CS
            int "i80 LCD Pin CS"
            default 3 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_PCLK
            int "i80 LCD Pin PCLK(WR)"
            default 5 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA0
            int "i80 LCD Pin D0"
            default 6 if IDF_TARGET_ESP32S3      
            
        config LCD_I80_PIN_NUM_DATA1
            int "i80 LCD Pin D1"
            default 7 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA2
            int "i80 LCD Pin D2"
            default 8 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA3
            int "i80 LCD Pin D3"
            default 9 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA4
            int "i80 LCD Pin D4"
            default 10 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA5
            int "i80 LCD Pin D5"
            default 11 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA6
            int "i80 LCD Pin D6"
            default 12 if IDF_TARGET_ESP32S3

        config LCD_I80_PIN_NUM_DATA7
            int "i80 LCD Pin D7"
            default 13 if IDF_TARGET_ESP32S3

        if LCD_I80_BUS_WIDTH > 8 || LCD_NT35510_DATA_WIDTH_16
            config LCD_I80_PIN_NUM_DATA8
                int "i80 LCD Pin D8"
                default 14 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA9
                int "i80 LCD Pin D9"
                default 15 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA10
                int "i80 LCD Pin D10"
                default 16 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA11
                int "i80 LCD Pin D11"
                default 17 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA12
                int "i80 LCD Pin D12"
                default 18 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA13
                int "i80 LCD Pin D13"
                default 19 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA14
                int "i80 LCD Pin D14"
                default 20 if IDF_TARGET_ESP32S3

            config LCD_I80_PIN_NUM_DATA15
                int "i80 LCD Pin D15"
                default 21 if IDF_TARGET_ESP32S3
        endif
    endmenu
endmenu
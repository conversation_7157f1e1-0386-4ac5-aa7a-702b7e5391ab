#ifndef _DRV_PCF8563_H
#define _DRV_PCF8563_H

//Support
//PCF8563
//DS3231

#include "hal_i2c.h"
#include "hal_service.h"
#include "esp_log.h"
#include "rtc_typedef.h"
#include "rtc_util.h"

typedef enum
{
    RTC_DEV_PCF8563,
    RTC_DEV_DS3231,
}rtc_dev_type_t;

typedef struct
{
    rtc_dev_type_t type;
    uint8_t addr;
    char *name;
}rtc_dev_t;

esp_err_t drv_rtc_init();
void drv_rtc_deinit(void);
void drv_rtc_task(void *pvParameters);
void drv_rtc_set(rtc_time_t *time);
const char* drv_rtc_get_weekstr(rtc_time_t *time);
const char *drv_rtc_get_weekstr_byidx(int index);
uint8_t drv_rtc_get_dayofweek(rtc_time_t *time);
bool drv_rtc_detect(void);
uint8_t drv_rtc_get_addr(void);

extern rtc_time_t rtc_time;


#endif



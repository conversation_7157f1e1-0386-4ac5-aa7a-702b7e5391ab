/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_Settings_Page(lv_ui *ui)
{
    //Write codes Settings_Page
    ui->Settings_Page = lv_obj_create(NULL);
    lv_obj_set_size(ui->Settings_Page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->Settings_Page, LV_SCROLLBAR_MODE_OFF);

    //Write style for Settings_Page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Settings_Page_tileview_1
    ui->Settings_Page_tileview_1 = lv_tileview_create(ui->Settings_Page);
    ui->Settings_Page_tileview_1_tile_current = lv_tileview_add_tile(ui->Settings_Page_tileview_1, 0, 0, LV_DIR_RIGHT);
    ui->Settings_Page_tileview_1_tile_speed = lv_tileview_add_tile(ui->Settings_Page_tileview_1, 1, 0, LV_DIR_LEFT | LV_DIR_RIGHT);
    ui->Settings_Page_tileview_1_tile_position = lv_tileview_add_tile(ui->Settings_Page_tileview_1, 2, 0, LV_DIR_LEFT | LV_DIR_RIGHT);
    ui->Settings_Page_tileview_1_tile_speed_track = lv_tileview_add_tile(ui->Settings_Page_tileview_1, 3, 0, LV_DIR_LEFT | LV_DIR_RIGHT);
    ui->Settings_Page_tileview_1_tile_posi_track = lv_tileview_add_tile(ui->Settings_Page_tileview_1, 4, 0, LV_DIR_LEFT);
    lv_obj_set_pos(ui->Settings_Page_tileview_1, 4, 5);
    lv_obj_set_size(ui->Settings_Page_tileview_1, 316, 226);
    lv_obj_set_scrollbar_mode(ui->Settings_Page_tileview_1, LV_SCROLLBAR_MODE_OFF);

    //Write style for Settings_Page_tileview_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_tileview_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_tileview_1, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_tileview_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_tileview_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_tileview_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Settings_Page_tileview_1, Part: LV_PART_SCROLLBAR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_tileview_1, 255, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_tileview_1, lv_color_hex(0xeaeff3), LV_PART_SCROLLBAR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_tileview_1, LV_GRAD_DIR_NONE, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_tileview_1, 0, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);





    //Write codes Settings_Page_meter_1
    ui->Settings_Page_meter_1 = lv_meter_create(ui->Settings_Page_tileview_1_tile_speed);
    // add scale ui->Settings_Page_meter_1_scale_0
    ui->Settings_Page_meter_1_scale_0 = lv_meter_add_scale(ui->Settings_Page_meter_1);
    lv_meter_set_scale_ticks(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, 41, 2, 6, lv_color_hex(0xC8B8B8));
    lv_meter_set_scale_major_ticks(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, 5, 2, 10, lv_color_hex(0xffff00), 3);
    lv_meter_set_scale_range(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, 0, 1000, 275, 135);

    // add scale line for ui->Settings_Page_meter_1_scale_0
    ui->Settings_Page_meter_1_scale_0_scaleline_0 = lv_meter_add_scale_lines(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, lv_color_hex(0x00FF47), lv_color_hex(0x01B51B), true, 0);
    lv_meter_set_indicator_start_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_scaleline_0, 0);
    lv_meter_set_indicator_end_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_scaleline_0, 600);

    // add scale line for ui->Settings_Page_meter_1_scale_0
    ui->Settings_Page_meter_1_scale_0_scaleline_1 = lv_meter_add_scale_lines(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, lv_color_hex(0xffff00), lv_color_hex(0xBC9C00), true, 0);
    lv_meter_set_indicator_start_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_scaleline_1, 600);
    lv_meter_set_indicator_end_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_scaleline_1, 800);

    // add scale line for ui->Settings_Page_meter_1_scale_0
    ui->Settings_Page_meter_1_scale_0_scaleline_2 = lv_meter_add_scale_lines(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, lv_color_hex(0xD8C500), lv_color_hex(0xD80021), true, 0);
    lv_meter_set_indicator_start_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_scaleline_2, 800);
    lv_meter_set_indicator_end_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_scaleline_2, 1000);

    // add needle images for ui->Settings_Page_meter_1_scale_0.
    ui->Settings_Page_meter_1_scale_0_ndline_0 = lv_meter_add_needle_img(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0, &_needle_alpha_210x210, 105, 105);
    lv_meter_set_indicator_value(ui->Settings_Page_meter_1, ui->Settings_Page_meter_1_scale_0_ndline_0, 1);
    lv_obj_set_pos(ui->Settings_Page_meter_1, 67, 29);
    lv_obj_set_size(ui->Settings_Page_meter_1, 173, 173);

    //Write style for Settings_Page_meter_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_meter_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_meter_1, 100, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Settings_Page_meter_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Settings_Page_meter_1, 14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Settings_Page_meter_1, 14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Settings_Page_meter_1, 14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Settings_Page_meter_1, 14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_meter_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Settings_Page_meter_1, Part: LV_PART_TICKS, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Settings_Page_meter_1, lv_color_hex(0x3affe7), LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_meter_1, &lv_font_montserratMedium_10, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_meter_1, 255, LV_PART_TICKS|LV_STATE_DEFAULT);

    //Write style for Settings_Page_meter_1, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_meter_1, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_meter_1, lv_color_hex(0x000000), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_meter_1, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write codes Settings_Page_slider_1
    ui->Settings_Page_slider_1 = lv_slider_create(ui->Settings_Page_tileview_1_tile_speed);
    lv_slider_set_range(ui->Settings_Page_slider_1, 0, 1000);
    lv_slider_set_mode(ui->Settings_Page_slider_1, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->Settings_Page_slider_1, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->Settings_Page_slider_1, 266, 25);
    lv_obj_set_size(ui->Settings_Page_slider_1, 6, 162);

    //Write style for Settings_Page_slider_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_slider_1, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_slider_1, lv_color_hex(0x34ffa4), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_slider_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_slider_1, 50, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->Settings_Page_slider_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_slider_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Settings_Page_slider_1, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_slider_1, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_slider_1, lv_color_hex(0x34ff98), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_slider_1, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_slider_1, 50, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for Settings_Page_slider_1, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_slider_1, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_slider_1, lv_color_hex(0x00ed5c), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_slider_1, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_slider_1, 50, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes Settings_Page_btn_send
    ui->Settings_Page_btn_send = lv_btn_create(ui->Settings_Page_tileview_1_tile_speed);
    ui->Settings_Page_btn_send_label = lv_label_create(ui->Settings_Page_btn_send);
    lv_label_set_text(ui->Settings_Page_btn_send_label, "发送目标速度");
    lv_label_set_long_mode(ui->Settings_Page_btn_send_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Settings_Page_btn_send_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Settings_Page_btn_send, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->Settings_Page_btn_send_label, LV_PCT(100));
    lv_obj_set_pos(ui->Settings_Page_btn_send, 172, 206);
    lv_obj_set_size(ui->Settings_Page_btn_send, 62, 25);

    //Write style for Settings_Page_btn_send, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_btn_send, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_btn_send, lv_color_hex(0x52595f), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_btn_send, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Settings_Page_btn_send, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_btn_send, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_btn_send, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Settings_Page_btn_send, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_btn_send, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_btn_send, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Settings_Page_btn_send, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Settings_Page_label_3
    ui->Settings_Page_label_3 = lv_label_create(ui->Settings_Page_tileview_1_tile_speed);
    lv_label_set_text(ui->Settings_Page_label_3, "目标速度");
    lv_label_set_long_mode(ui->Settings_Page_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Settings_Page_label_3, 221, 201);
    lv_obj_set_size(ui->Settings_Page_label_3, 100, 32);

    //Write style for Settings_Page_label_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Settings_Page_label_3, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_label_3, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Settings_Page_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Settings_Page_label_3, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Settings_Page_label_2
    ui->Settings_Page_label_2 = lv_label_create(ui->Settings_Page_tileview_1_tile_speed);
    lv_label_set_text(ui->Settings_Page_label_2, "1000");
    lv_label_set_long_mode(ui->Settings_Page_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Settings_Page_label_2, 125, 86);
    lv_obj_set_size(ui->Settings_Page_label_2, 62, 64);

    //Write style for Settings_Page_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Settings_Page_label_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_label_2, &lv_font_Antonio_Regular_13, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Settings_Page_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Settings_Page_label_2, 28, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui->Settings_Page_label_2, &_kmbg_62x64, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(ui->Settings_Page_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_recolor_opa(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);



    //Write codes Settings_Page_arc_1
    ui->Settings_Page_arc_1 = lv_arc_create(ui->Settings_Page_tileview_1_tile_position);
    lv_arc_set_mode(ui->Settings_Page_arc_1, LV_ARC_MODE_NORMAL);
    lv_arc_set_range(ui->Settings_Page_arc_1, 0, 360);
    lv_arc_set_bg_angles(ui->Settings_Page_arc_1, 0, 360);
    lv_arc_set_value(ui->Settings_Page_arc_1, 180);
    lv_arc_set_rotation(ui->Settings_Page_arc_1, 0);
    lv_obj_set_pos(ui->Settings_Page_arc_1, 77, 37);
    lv_obj_set_size(ui->Settings_Page_arc_1, 162, 157);

    //Write style for Settings_Page_arc_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui->Settings_Page_arc_1, 12, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui->Settings_Page_arc_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_arc_color(ui->Settings_Page_arc_1, lv_color_hex(0x1cc103), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_arc_1, 6, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_arc_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Settings_Page_arc_1, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_arc_width(ui->Settings_Page_arc_1, 12, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui->Settings_Page_arc_1, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_arc_color(ui->Settings_Page_arc_1, lv_color_hex(0x00f71c), LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for Settings_Page_arc_1, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Settings_Page_arc_1, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Settings_Page_arc_1, lv_color_hex(0x00f71c), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Settings_Page_arc_1, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_all(ui->Settings_Page_arc_1, 5, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes Settings_Page_label_5
    ui->Settings_Page_label_5 = lv_label_create(ui->Settings_Page_tileview_1_tile_position);
    lv_label_set_text(ui->Settings_Page_label_5, "180°");
    lv_label_set_long_mode(ui->Settings_Page_label_5, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Settings_Page_label_5, 105, 104);
    lv_obj_set_size(ui->Settings_Page_label_5, 100, 26);

    //Write style for Settings_Page_label_5, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Settings_Page_label_5, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_label_5, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_label_5, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Settings_Page_label_5, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Settings_Page_label_5, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);





    //Write codes Settings_Page_list_1
    ui->Settings_Page_list_1 = lv_list_create(ui->Settings_Page);
    ui->Settings_Page_list_1_item0 = lv_list_add_btn(ui->Settings_Page_list_1, LV_SYMBOL_WIFI, "网络连接");
    ui->Settings_Page_list_1_item1 = lv_list_add_btn(ui->Settings_Page_list_1, LV_SYMBOL_LOOP, "加减速设置");
    ui->Settings_Page_list_1_item2 = lv_list_add_btn(ui->Settings_Page_list_1, LV_SYMBOL_SAVE, "save_3");
    ui->Settings_Page_list_1_item3 = lv_list_add_btn(ui->Settings_Page_list_1, LV_SYMBOL_SAVE, "save_4");
    ui->Settings_Page_list_1_item4 = lv_list_add_btn(ui->Settings_Page_list_1, LV_SYMBOL_SAVE, "save_4");
    ui->Settings_Page_list_1_item5 = lv_list_add_btn(ui->Settings_Page_list_1, LV_SYMBOL_SAVE, "save_5");
    lv_obj_set_pos(ui->Settings_Page_list_1, -5, 31);
    lv_obj_set_size(ui->Settings_Page_list_1, 328, 208);
    lv_obj_set_scrollbar_mode(ui->Settings_Page_list_1, LV_SCROLLBAR_MODE_ON);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_main_main_default
    static lv_style_t style_Settings_Page_list_1_main_main_default;
    ui_init_style(&style_Settings_Page_list_1_main_main_default);

    lv_style_set_pad_top(&style_Settings_Page_list_1_main_main_default, 5);
    lv_style_set_pad_left(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_pad_right(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_border_width(&style_Settings_Page_list_1_main_main_default, 0);
    lv_style_set_radius(&style_Settings_Page_list_1_main_main_default, 3);
    lv_style_set_shadow_width(&style_Settings_Page_list_1_main_main_default, 0);
    lv_obj_add_style(ui->Settings_Page_list_1, &style_Settings_Page_list_1_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_main_scrollbar_default
    static lv_style_t style_Settings_Page_list_1_main_scrollbar_default;
    ui_init_style(&style_Settings_Page_list_1_main_scrollbar_default);

    lv_style_set_radius(&style_Settings_Page_list_1_main_scrollbar_default, 3);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_main_scrollbar_default, 255);
    lv_style_set_bg_color(&style_Settings_Page_list_1_main_scrollbar_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_Settings_Page_list_1_main_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->Settings_Page_list_1, &style_Settings_Page_list_1_main_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_extra_btns_main_default
    static lv_style_t style_Settings_Page_list_1_extra_btns_main_default;
    ui_init_style(&style_Settings_Page_list_1_extra_btns_main_default);

    lv_style_set_pad_top(&style_Settings_Page_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_left(&style_Settings_Page_list_1_extra_btns_main_default, 10);
    lv_style_set_pad_right(&style_Settings_Page_list_1_extra_btns_main_default, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_extra_btns_main_default, 10);
    lv_style_set_border_width(&style_Settings_Page_list_1_extra_btns_main_default, 1);
    lv_style_set_border_opa(&style_Settings_Page_list_1_extra_btns_main_default, 255);
    lv_style_set_border_color(&style_Settings_Page_list_1_extra_btns_main_default, lv_color_hex(0x6f6969));
    lv_style_set_border_side(&style_Settings_Page_list_1_extra_btns_main_default, LV_BORDER_SIDE_FULL);
    lv_style_set_text_color(&style_Settings_Page_list_1_extra_btns_main_default, lv_color_hex(0xfef6ea));
    lv_style_set_text_font(&style_Settings_Page_list_1_extra_btns_main_default, &lv_font_montserratMedium_18);
    lv_style_set_text_opa(&style_Settings_Page_list_1_extra_btns_main_default, 255);
    lv_style_set_radius(&style_Settings_Page_list_1_extra_btns_main_default, 0);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_extra_btns_main_default, 0);
    lv_obj_add_style(ui->Settings_Page_list_1_item5, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->Settings_Page_list_1_item4, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->Settings_Page_list_1_item3, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->Settings_Page_list_1_item2, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->Settings_Page_list_1_item1, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->Settings_Page_list_1_item0, &style_Settings_Page_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_Settings_Page_list_1_extra_texts_main_default
    static lv_style_t style_Settings_Page_list_1_extra_texts_main_default;
    ui_init_style(&style_Settings_Page_list_1_extra_texts_main_default);

    lv_style_set_pad_top(&style_Settings_Page_list_1_extra_texts_main_default, 6);
    lv_style_set_pad_left(&style_Settings_Page_list_1_extra_texts_main_default, 5);
    lv_style_set_pad_right(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_pad_bottom(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_border_width(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_text_color(&style_Settings_Page_list_1_extra_texts_main_default, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_Settings_Page_list_1_extra_texts_main_default, &lv_font_montserratMedium_18);
    lv_style_set_text_opa(&style_Settings_Page_list_1_extra_texts_main_default, 255);
    lv_style_set_radius(&style_Settings_Page_list_1_extra_texts_main_default, 3);
    lv_style_set_transform_width(&style_Settings_Page_list_1_extra_texts_main_default, 0);
    lv_style_set_bg_opa(&style_Settings_Page_list_1_extra_texts_main_default, 0);

    //Write codes Settings_Page_imgbtn_1
    ui->Settings_Page_imgbtn_1 = lv_imgbtn_create(ui->Settings_Page);
    lv_obj_add_flag(ui->Settings_Page_imgbtn_1, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->Settings_Page_imgbtn_1, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_26x26, NULL);
    ui->Settings_Page_imgbtn_1_label = lv_label_create(ui->Settings_Page_imgbtn_1);
    lv_label_set_text(ui->Settings_Page_imgbtn_1_label, "");
    lv_label_set_long_mode(ui->Settings_Page_imgbtn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Settings_Page_imgbtn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Settings_Page_imgbtn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->Settings_Page_imgbtn_1, 4, 5);
    lv_obj_set_size(ui->Settings_Page_imgbtn_1, 26, 26);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Settings_Page_imgbtn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Settings_Page_imgbtn_1, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Settings_Page_imgbtn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->Settings_Page_imgbtn_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->Settings_Page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->Settings_Page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->Settings_Page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->Settings_Page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for Settings_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->Settings_Page_imgbtn_1, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->Settings_Page_imgbtn_1, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //The custom code of Settings_Page.


    //Update current screen layout.
    lv_obj_update_layout(ui->Settings_Page);


}

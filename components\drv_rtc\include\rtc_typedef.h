#ifndef _RTC_TYPEDEF_H
#define _RTC_TYPEDEF_H

#include <stdint.h>

#define LongToBin(n) \
	(\
	((n >> 21) & 0x80) | \
	((n >> 18) & 0x40) | \
	((n >> 15) & 0x20) | \
	((n >> 12) & 0x10) | \
	((n >> 9) & 0x08) | \
	((n >> 6) & 0x04) | \
	((n >> 3) & 0x02) | \
	((n ) & 0x01) \
	)
#define B(n) LongToBin(0x##n##l)

typedef struct
{
    uint16_t tm_year; 
    uint8_t tm_mon;
    uint8_t tm_mday;
    uint8_t tm_wday;
    uint8_t tm_hour;
    uint8_t tm_min;
    uint8_t tm_sec;
} rtc_time_t;

#endif

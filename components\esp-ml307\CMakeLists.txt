idf_component_register(
    SRCS
        "ml307_at_modem.cc"
        "ml307_ssl_transport.cc"
        "ml307_http.cc"
        "ml307_mqtt.cc"
        "ml307_udp.cc"
        "web_socket.cc"
        "tls_transport.cc"
        "tcp_transport.cc"
        "esp_http.cc"
        "esp_mqtt.cc"
        "esp_udp.cc"
    INCLUDE_DIRS
        "include"
    PRIV_INCLUDE_DIRS
        "."
    REQUIRES
        "esp_driver_gpio"
        "esp_driver_uart"
        "esp-tls"
        "esp_http_client"
        "mqtt"
)

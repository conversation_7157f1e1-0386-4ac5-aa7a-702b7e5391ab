#include <stdio.h>
#include <string.h>
#include "drv_adc.h"
#include "hal_service.h"
#include "board_pins_user.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_adc/adc_continuous.h"
#include "driver/gpio.h"
#include "esp_log.h"

#define TAG "drv_adc"

#define READ_BUFF_LEN 1024
#define ADC_FREQ_HZ (16000 * 2)

#define ADC_UNIT ADC_UNIT_1
#define ADC_CONV_MODE ADC_CONV_SINGLE_UNIT_1
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#define ADC_ATTEN ADC_ATTEN_DB_11
#define ADC_BIT_WIDTH ADC_BITWIDTH_12

// static functions
static void adc_read_task(void *pvParameters);

// static functions

static SemaphoreHandle_t s_evt_sem = NULL;
static adc_continuous_handle_t adc_handle = NULL;

static drv_adc_handle_t adc_report_handler = NULL;

static adc_channel_t channel[DRV_ADC_TYPE_MAX] = {ADC_CHANNEL_0, ADC_CHANNEL_1};
static bool run_flag;

uint8_t *adc_buff;

periph_service_handle_t drv_adc_service;

static bool IRAM_ATTR s_conv_done_cb(adc_continuous_handle_t handle, const adc_continuous_evt_data_t *edata, void *user_data)
{
    BaseType_t mustYield = pdFALSE;

    // Notify that ADC continuous driver has done enough number of conversions
    xSemaphoreGiveFromISR(s_evt_sem, &mustYield);

    return (mustYield == pdTRUE);
}

void drv_adc_init()
{
    gpio_config_t mic_gpio_config = {
        .mode = GPIO_MODE_DEF_INPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pin_bit_mask = 1ULL << DEF_AMIC_GPIO};
    ESP_ERROR_CHECK(gpio_config(&mic_gpio_config));

    gpio_config_t bat_adc_gpio_config = {
        .mode = GPIO_MODE_DEF_INPUT,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pin_bit_mask = 1ULL << DEF_BATTERY_ADC_GPIO};
    ESP_ERROR_CHECK(gpio_config(&bat_adc_gpio_config));

    adc_buff = (uint8_t *)malloc(READ_BUFF_LEN);
    if (adc_buff == NULL)
        return;

    memset(adc_buff, 0xcc, READ_BUFF_LEN);

    s_evt_sem = xSemaphoreCreateBinary();

    adc_continuous_handle_cfg_t adc_config = {
        .max_store_buf_size = sizeof(uint32_t) * READ_BUFF_LEN,
        .conv_frame_size = READ_BUFF_LEN,
    };
    ESP_ERROR_CHECK(adc_continuous_new_handle(&adc_config, &adc_handle));

    adc_continuous_config_t dig_cfg = {
        .sample_freq_hz = ADC_FREQ_HZ,
        .conv_mode = ADC_CONV_MODE,
        .format = ADC_OUTPUT_TYPE,
    };

    dig_cfg.pattern_num = DRV_ADC_TYPE_MAX;
    adc_digi_pattern_config_t adc_pattern[DRV_ADC_TYPE_MAX] = {0};

    for (int i = 0; i < dig_cfg.pattern_num; i++)
    {
        adc_pattern[i].atten = ADC_ATTEN;
        adc_pattern[i].channel = channel[i] & 0x7;
        adc_pattern[i].unit = ADC_UNIT;
        adc_pattern[i].bit_width = ADC_BIT_WIDTH;

        ESP_LOGI(TAG, "adc_pattern[%d].atten is :%" PRIx8, i, adc_pattern[i].atten);
        ESP_LOGI(TAG, "adc_pattern[%d].channel is :%" PRIx8, i, adc_pattern[i].channel);
        ESP_LOGI(TAG, "adc_pattern[%d].unit is :%" PRIx8, i, adc_pattern[i].unit);
    }

    dig_cfg.adc_pattern = &adc_pattern;
    ESP_ERROR_CHECK(adc_continuous_config(adc_handle, &dig_cfg));

    hal_service_config_t recoder_service_cfg = {
        .task_stack = 8 * 1024,
        .task_prio = 5,
        .task_core = 1,
        .task_func = adc_read_task,
        .extern_stack = true,
        .service_start = NULL,
        .service_stop = NULL,
        .service_destroy = NULL,
        .service_name = "adc_serv",
        .user_data = (void *)1,
    };

    drv_adc_service = hal_service_create(&recoder_service_cfg);
}

void drv_adc_deinit()
{
    run_flag = false;

    if (adc_report_handler)
    {
        adc_report_handler = NULL;
    }

    if (drv_adc_service)
    {
        hal_service_destroy(drv_adc_service);
        drv_adc_service = NULL;
    }
}

void drv_adc_handle_set(drv_adc_handle_t *handle)
{
    adc_report_handler = handle;
}

static void adc_read_task(void *pvParameters)
{
    periph_service_handle_t serv_handle = (periph_service_handle_t)pvParameters;
    int delay = periph_service_get_data(serv_handle);

    esp_err_t ret;
    uint32_t ret_num = 0;

    adc_continuous_evt_cbs_t cbs = {
        .on_conv_done = s_conv_done_cb,
    };
    ESP_ERROR_CHECK(adc_continuous_register_event_callbacks(adc_handle, &cbs, NULL));
    ESP_ERROR_CHECK(adc_continuous_start(adc_handle));

    run_flag = true;
    while (run_flag)
    {
        if (xSemaphoreTake(s_evt_sem, pdMS_TO_TICKS(100)) == pdTRUE)
        {
            ret = adc_continuous_read(adc_handle, adc_buff, READ_BUFF_LEN, &ret_num, 0);

            if (ret == ESP_OK)
            {
                // ESP_LOGI("TASK", "ret is %x, ret_num is %" PRIu32 " bytes", ret, ret_num);
                for (int i = 0; i < ret_num; i += SOC_ADC_DIGI_RESULT_BYTES)
                {
                    adc_digi_output_data_t *p = (adc_digi_output_data_t *)&adc_buff[i];
                    uint8_t chan_num = p->type2.channel;
                    uint16_t data = p->type2.data;
                    /* Check the channel number validation, the data is invalid if the channel num exceed the maximum channel */
                    if (chan_num < SOC_ADC_CHANNEL_NUM(ADC_UNIT))
                    {
                        // ESP_LOGI(TAG, "Channel: %" PRId32 ", Value: %" PRId32, chan_num, data);
                        if (chan_num == ADC_CHANNEL_0)
                        {
                            if (adc_report_handler)
                                adc_report_handler(DRV_ADC_TYPE_BAT, &data);
                        }
                        else if (chan_num == ADC_CHANNEL_1)
                        {
                            if (adc_report_handler)
                                adc_report_handler(DRV_ADC_TYPE_AMIC, &data);
                        }
                    }
                    else
                    {
                        ESP_LOGW(TAG, "Invalid data [%" PRIu32 "_%" PRIx32 "]", chan_num, data);
                    }
                }
            }
        }
        else
        {
            ESP_LOGW(TAG, "adc_continuous_read timeout, try restart!");
            ESP_ERROR_CHECK(adc_continuous_stop(adc_handle));
            ESP_ERROR_CHECK(adc_continuous_start(adc_handle));
        }

        if (!run_flag)
            break;
    }

    if (adc_buff)
    {
        free(adc_buff);
        adc_buff = NULL;
    }

    if (s_evt_sem)
        vSemaphoreDelete(s_evt_sem);

    ESP_ERROR_CHECK(adc_continuous_stop(adc_handle));
    ESP_ERROR_CHECK(adc_continuous_deinit(adc_handle));

    ESP_LOGI(TAG, "adc service destroyed");
    vTaskDelete(NULL);
}

# This file describes the settings to be used by the documentation system
# doxygen (www.doxygen.org) for a project
#

PROJECT_NAME			= "mpg123 library family"
OUTPUT_DIRECTORY		= .
CREATE_SUBDIRS			= NO
OUTPUT_LANGUAGE			= English
FULL_PATH_NAMES			= NO

INPUT = \
	doxy_examples.c \
	examples/mpg123_to_out123.c \
	examples/mpglib.c \
	examples/scan.c \
	examples/feedseek.c \
	examples/extract_frames.c \
	examples/id3dump.c \
	../src/include/mpg123.h \
	../src/include/fmt123.h \
	../src/include/out123.h \
	../src/include/syn123.h \
#OPTIMIZE_OUTPUT_FOR_C	= YES
EXTRACT_ALL				= NO
HIDE_UNDOC_MEMBERS		= NO
QUIET					= YES
WARNINGS				= YES
SOURCE_BROWSER = YES

GENERATE_HTML			= YES
HTML_OUTPUT				= html
DISABLE_INDEX			= YES
SORT_MEMBER_DOCS		= NO
SORT_BRIEF_DOCS			= NO
ENUM_VALUES_PER_LINE	= 2


ENABLE_PREPROCESSING   = YES
MACRO_EXPANSION        = YES
EXPAND_ONLY_PREDEF     = YES
PREDEFINED             = EXPORT= MPG123_ENUM_API

GENERATE_HTML			= YES
GENERATE_LATEX			= YES
GENERATE_RTF			= NO
GENERATE_MAN			= YES
MAN_LINKS				= YES
GENERATE_XML			= NO

HTML_HEADER = doxyhead.xhtml
HTML_FOOTER = doxyfoot.xhtml
HTML_FILE_EXTENSION = .shtml

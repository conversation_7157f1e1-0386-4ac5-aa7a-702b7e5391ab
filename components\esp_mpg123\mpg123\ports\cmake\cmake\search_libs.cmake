function(search_libs function output_var)
    if(${output_var})
        return()
    endif()
    set(LIBS "" ${ARGN})
    foreach(CMAKE_REQUIRED_LIBRARIES IN LISTS ${LIBS})
        message(STATUS "######## ${CMAKE_REQUIRED_LIBRARIES} ${HAVE_${function}}")
        check_function_exists(${function} HAVE_${function})
        if(HAVE_${function})
            set(${output_var} ${CMAKE_REQUIRED_LIBRARIES} CACHE INTERNAL "")
            return()
        endif()
    endforeach()
endfunction()

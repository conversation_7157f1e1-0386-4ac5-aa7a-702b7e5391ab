#include <stdio.h>
#include "drv_beep.h"
#include "hal_service.h"
#include "driver/gpio.h"
#include "board_pins_user.h"
#include "esp_log.h"

#if (DEF_BEEP_GPIO < 0)
#include "drv_pca9557.h"
#endif

#define TAG "beep"

// static functions
static void beep_serv_task(void *arg);

// static functions

typedef struct
{
    xQueueHandle serv_q;
    bool running;
} beep_serv_t;

beep_serv_t serv_param_beep;
periph_service_handle_t serv_beep;

void drv_beep_init()
{
#if (DEF_BEEP_GPIO >= 0)
    gpio_config_t bepp_gpio_config = {
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pin_bit_mask = 1ULL << DEF_BEEP_GPIO};
    ESP_ERROR_CHECK(gpio_config(&bepp_gpio_config));
    gpio_set_level(DEF_BEEP_GPIO, 0);
#else
    drv_pca9557_pinmode(EXTEND_PIN_5_BEEP_CTRL, EXTEND_OUTPUT);
    drv_pca9557_write(EXTEND_PIN_5_BEEP_CTRL, EXTEND_LOW);
#endif

    serv_param_beep.running = false;
    serv_param_beep.serv_q = xQueueCreate(16, sizeof(drv_beep_msg_t));

    hal_service_config_t beep_service_cfg = {
        .task_stack = 4 * 1024,
        .task_prio = 4,
        .task_core = 0,
        .task_func = beep_serv_task,
        .extern_stack = true,
        .service_start = NULL,
        .service_stop = NULL,
        .service_destroy = NULL,
        .service_name = "play_ctrl_serv",
        .user_data = (void *)&serv_param_beep,
    };

    serv_beep = hal_service_create(&beep_service_cfg);
}

void drv_beep_deinit()
{
    if (serv_beep)
    {

        if (serv_param_beep.serv_q)
        {
            serv_param_beep.running = false;
            vQueueDelete(serv_param_beep.serv_q);
        }
        hal_service_destroy(serv_beep);
        serv_beep = NULL;
    }

#if (DEF_BEEP_GPIO >= 0)

#else
    drv_pca9557_write(EXTEND_PIN_5_BEEP_CTRL, EXTEND_LOW);
#endif
}

void drv_beep_send(drv_beep_msg_t *msg)
{
    BaseType_t xReturn = pdPASS;
    xReturn = xQueueSend(serv_param_beep.serv_q, msg, 0);

    if (xReturn != pdPASS)
    {
        ESP_LOGE(TAG, "beep commit failed");
    }
}

/**********************************************STATIC***********************************************/
static void beep_once(drv_beep_msg_t *msg)
{
    if (msg == NULL)
        return;
#if (DEF_BEEP_GPIO >= 0)
    gpio_set_level(DEF_BEEP_GPIO, 1);
    vTaskDelay(pdMS_TO_TICKS(msg->delay));
    gpio_set_level(DEF_BEEP_GPIO, 0);
    vTaskDelay(pdMS_TO_TICKS(msg->delay));
#else
    drv_pca9557_write(EXTEND_PIN_5_BEEP_CTRL, EXTEND_HIGH);
    vTaskDelay(pdMS_TO_TICKS(msg->delay));
    drv_pca9557_write(EXTEND_PIN_5_BEEP_CTRL, EXTEND_LOW);
    vTaskDelay(pdMS_TO_TICKS(msg->delay));
#endif
}

static void beep_stop()
{
#if (DEF_BEEP_GPIO >= 0)
    gpio_set_level(DEF_BEEP_GPIO, 0);
#else
    drv_pca9557_write(EXTEND_PIN_5_BEEP_CTRL, EXTEND_LOW);
#endif
}

static void beep_serv_task(void *arg)
{
    periph_service_handle_t serv_handle = (periph_service_handle_t)arg;
    beep_serv_t *service = periph_service_get_data(serv_handle);

    BaseType_t xReturn = pdFALSE;

    drv_beep_msg_t msg = {0};

    service->running = true;
    while (service->running)
    {
        xReturn = xQueueReceive(service->serv_q, &msg, portMAX_DELAY);
        if (xReturn != pdTRUE)
            continue;

        for (int i = 0; i < msg.count; i++)
        {
            beep_once(&msg);
        }
    }

    ESP_LOGI(TAG, "beep service stopped!");
    beep_stop();
    vTaskDelete(NULL);
}

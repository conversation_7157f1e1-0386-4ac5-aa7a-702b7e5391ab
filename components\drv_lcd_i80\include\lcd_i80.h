#ifndef _LCD_I80_H
#define _LCD_I80_H

#include "esp_err.h"
#include "esp_log.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

#define LCD_PIXEL_CLOCK_HZ  CONFIG_LCD_I80_PIXEL_CLOCK_HZ

// The pixel number in horizontal and vertical
// #define LCD_H_RES              320//240
// #define LCD_V_RES              240//280
// Bit number used to represent command and parameter
#if CONFIG_LCD_I80_CONTROLLER_ST7789
#define LCD_CMD_BITS           8
#define LCD_PARAM_BITS         8
#elif CONFIG_LCD_I80_CONTROLLER_NT35510
#define LCD_CMD_BITS           16
#define LCD_PARAM_BITS         16
#elif CONFIG_LCD_I80_CONTROLLER_ILI9341
#define LCD_CMD_BITS           8
#define LCD_PARAM_BITS         8
#endif

#define LCD_DMA_BURST_SIZE         64 // 16, 32, 64. Higher burst size can improve the performance when the DMA buffer comes from PSRAM

typedef struct
{
    int width;
    int height;
    int display_row;
    uint8_t bkl;
    esp_lcd_panel_io_handle_t io_handle;
    esp_lcd_panel_handle_t panel_handle;
    void(*transcallback)();
    void * user_data;
}lcd_i80_t;

void lcd_i80_init(lcd_i80_t *lcd);
void lcd_i80_address_set(lcd_i80_t *lcd, unsigned int x1, unsigned int y1, unsigned int x2, unsigned int y2);
void lcd_i80_clear(lcd_i80_t *lcd, uint16_t color);
void lcd_i80_draw_point(lcd_i80_t *lcd, int x, int y, uint16_t color);
void lcd_i80_draw_line(lcd_i80_t *lcd, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);
void lcd_i80_push_image(lcd_i80_t *lcd, int x_start, int y_start, int x_end, int y_end, const void *color_data);
void * lcd_i80_alloc_drawbuff(lcd_i80_t *lcd, size_t buff_size);
void lcd_i80_backlight_set(lcd_i80_t *lcd, uint8_t val);

uint8_t lcd_i80_get_backlight(lcd_i80_t *lcd);

extern lcd_i80_t lcd;

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
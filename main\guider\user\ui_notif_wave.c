#include "ui_user_inc.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "ui_wave";
#define WAVE_BUFFER_SIZE 50        // 缓存数据点数量

// 波形数据缓存
static int32_t wave_data_buffer[WAVE_DATA_MAX][WAVE_BUFFER_SIZE];
static uint16_t wave_buffer_index[WAVE_DATA_MAX] = {0};
static wave_data_type_t current_wave_type = WAVE_DATA_CURRENT; // 当前显示的波形类型

// 自动缩放和分辨率控制
static bool auto_scale_enabled = false;  // 自动缩放开关状态
static uint16_t chart_point_count = 50;  // 当前图表点数（分辨率）
#define MIN_POINT_COUNT 10               // 最小点数
#define MAX_POINT_COUNT 200              // 最大点数
#define POINT_COUNT_STEP 10              // 点数调节步长
static volatile bool chart_updating = false;  // 图表更新标志

// 波形配置表 - 便于扩展新的数据类型
typedef struct {
    wave_data_type_t type;
    const char *name;
    lv_color_t color;
    int32_t min_range;
    int32_t max_range;
    float scale_factor;  // 缩放因子
} wave_config_t;

static const wave_config_t wave_configs[WAVE_DATA_MAX] = {
    [WAVE_DATA_CURRENT] = {
        .type = WAVE_DATA_CURRENT,
        .name = "电流波形",
        .color = LV_COLOR_MAKE(255, 0, 52),  // 红色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f
    },
    [WAVE_DATA_SPEED] = {
        .type = WAVE_DATA_SPEED,
        .name = "速度波形",
        .color = LV_COLOR_MAKE(47, 53, 218), // 蓝色
        .min_range = -500,
        .max_range = 500,
        .scale_factor = 0.1f  
    },
    [WAVE_DATA_POSITION] = {
        .type = WAVE_DATA_POSITION,
        .name = "位置波形",
        .color = LV_COLOR_MAKE(0, 255, 0),   // 绿色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f 
    },
    [WAVE_DATA_VOLTAGE] = {
        .type = WAVE_DATA_VOLTAGE,
        .name = "电压波形",
        .color = LV_COLOR_MAKE(255, 165, 0), // 橙色
        .min_range = 0,
        .max_range = 150,
        .scale_factor = 1.0f
    },
    [WAVE_DATA_TEMPERATURE] = {
        .type = WAVE_DATA_TEMPERATURE,
        .name = "温度波形",
        .color = LV_COLOR_MAKE(255, 0, 255), // 紫色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f
    }
};

/**
 * @brief 添加波形数据到缓存
 */
static void wave_add_data_to_buffer(wave_data_type_t type, int32_t value)
{
    if (type >= WAVE_DATA_MAX) {
        return;
    }

    // 应用缩放因子
    int32_t scaled_value = (int32_t)(value * wave_configs[type].scale_factor);

    // 添加到缓存
    wave_data_buffer[type][wave_buffer_index[type]] = scaled_value;
    wave_buffer_index[type] = (wave_buffer_index[type] + 1) % WAVE_BUFFER_SIZE;
}

/**
 * @brief 更新图表显示
 */
static void wave_update_chart_display(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || !ui->Wave_Page_chart_1_series || current_wave_type >= WAVE_DATA_MAX) {
        ESP_LOGW(TAG, "Chart or series not available for display update");
        return;
    }

    const wave_config_t *config = &wave_configs[current_wave_type];

    // 设置图表范围
    lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y,
                       config->min_range, config->max_range);

    // 清空现有数据，从头开始显示 - 修复：使用正确的数据系列指针
    lv_chart_set_all_value(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series, LV_CHART_POINT_NONE);

    // 更新数据系列颜色 - 修复：使用正确的数据系列指针
    lv_chart_set_series_color(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series, config->color);

    // 使用更安全的刷新机制
    lv_chart_refresh(ui->Wave_Page_chart_1);

    // 只进行必要的重绘，避免过度刷新
    lv_obj_invalidate(ui->Wave_Page_chart_1);

    // 更新布局，确保所有变更生效
    lv_obj_update_layout(ui->Wave_Page_chart_1);
}

/**
 * @brief 设置当前显示的波形类型
 */
void wave_set_display_type(wave_data_type_t type)
{
    if (type >= WAVE_DATA_MAX) {
        return;
    }
    current_wave_type = type;
    wave_update_chart_display();

    ESP_LOGI(TAG, "Switch to wave type: %s", wave_configs[type].name);
}

/**
 * @brief 切换自动缩放开关状态
 */
void wave_toggle_auto_scale(void)
{
    lv_ui *ui = &guider_ui;

    auto_scale_enabled = !auto_scale_enabled;

    // 更新按键颜色
    if (auto_scale_enabled) {
        // 开启状态：绿色
        lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x00FF00), LV_STATE_DEFAULT);
        ESP_LOGI(TAG, "Auto scale enabled");
    } else {
        // 关闭状态：蓝色
        lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x0000FF), LV_STATE_DEFAULT);
        ESP_LOGI(TAG, "Auto scale disabled");
    }
}

/**
 * @brief 增加图表分辨率（减少点数）
 */
void wave_increase_resolution(void)
{
    lv_ui *ui = &guider_ui;

    if (chart_point_count > MIN_POINT_COUNT) {
        chart_point_count -= POINT_COUNT_STEP;
        if (chart_point_count < MIN_POINT_COUNT) {
            chart_point_count = MIN_POINT_COUNT;
        }

        // 更新图表点数
        lv_chart_set_point_count(ui->Wave_Page_chart_1, chart_point_count);

        // 重新创建数据系列以适应新的点数
        if (ui->Wave_Page_chart_1_series) {
            lv_chart_remove_series(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series);
        }
        ui->Wave_Page_chart_1_series = lv_chart_add_series(ui->Wave_Page_chart_1,
                                                           wave_configs[current_wave_type].color,
                                                           LV_CHART_AXIS_PRIMARY_Y);

        ESP_LOGI(TAG, "Resolution increased, point count: %d", chart_point_count);
    } else {
        ESP_LOGW(TAG, "Already at maximum resolution");
    }
}

/**
 * @brief 减少图表分辨率（增加点数）
 */
void wave_decrease_resolution(void)
{
    lv_ui *ui = &guider_ui;

    if (chart_point_count < MAX_POINT_COUNT) {
        chart_point_count += POINT_COUNT_STEP;
        if (chart_point_count > MAX_POINT_COUNT) {
            chart_point_count = MAX_POINT_COUNT;
        }

        // 更新图表点数
        lv_chart_set_point_count(ui->Wave_Page_chart_1, chart_point_count);

        // 重新创建数据系列以适应新的点数
        if (ui->Wave_Page_chart_1_series) {
            lv_chart_remove_series(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series);
        }
        ui->Wave_Page_chart_1_series = lv_chart_add_series(ui->Wave_Page_chart_1,
                                                           wave_configs[current_wave_type].color,
                                                           LV_CHART_AXIS_PRIMARY_Y);

        ESP_LOGI(TAG, "Resolution decreased, point count: %d", chart_point_count);
    } else {
        ESP_LOGW(TAG, "Already at minimum resolution");
    }
}

/**
 * @brief 获取当前显示的波形类型
 */
wave_data_type_t wave_get_display_type(void)
{
    return current_wave_type;
}

/**
 * @brief 获取波形配置
 */
const wave_config_t* wave_get_config(wave_data_type_t type)
{
    if (type >= WAVE_DATA_MAX) {
        return NULL;
    }
    return &wave_configs[type];
}

/**
 * @brief 计算当前波形数据的实际范围
 * @param type 波形数据类型
 * @param min_val 输出最小值
 * @param max_val 输出最大值
 * @return true 如果有有效数据，false 如果没有数据
 */
static bool wave_calculate_data_range(wave_data_type_t type, int32_t *min_val, int32_t *max_val)
{
    if (type >= WAVE_DATA_MAX || !min_val || !max_val) {
        return false;
    }

    // 检查是否有数据
    uint16_t buffer_index = wave_buffer_index[type];
    if (buffer_index == 0) {
        // 没有数据，使用配置的默认范围
        *min_val = wave_configs[type].min_range;
        *max_val = wave_configs[type].max_range;
        return false;
    }

    // 初始化最大最小值
    int32_t min = wave_data_buffer[type][0];
    int32_t max = wave_data_buffer[type][0];

    // 遍历所有有效数据点
    uint16_t data_count = (buffer_index >= WAVE_BUFFER_SIZE) ? WAVE_BUFFER_SIZE : buffer_index;
    for (uint16_t i = 0; i < data_count; i++) {
        int32_t value = wave_data_buffer[type][i];
        if (value < min) min = value;
        if (value > max) max = value;
    }

    *min_val = min;
    *max_val = max;

    ESP_LOGI(TAG, "Data range for type %d: min=%ld, max=%ld, count=%d", type, min, max, data_count);
    return true;
}

/**
 * @brief 执行波形自动缩放，使波形居中显示
 */
void wave_auto_scale(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || !ui->Wave_Page_chart_1_series || current_wave_type >= WAVE_DATA_MAX) {
        ESP_LOGW(TAG, "Chart or series not available for auto scale");
        return;
    }

    int32_t data_min, data_max;
    bool has_data = wave_calculate_data_range(current_wave_type, &data_min, &data_max);

    if (!has_data) {
        ESP_LOGW(TAG, "No data available for auto scale, using default range");
        // 使用默认配置范围
        const wave_config_t *config = &wave_configs[current_wave_type];
        data_min = config->min_range;
        data_max = config->max_range;
    }

    // 如果最大值和最小值相同，添加一些范围
    if (data_max == data_min) {
        if (data_max == 0) {
            data_min = -10;
            data_max = 10;
        } else {
            int32_t margin = abs(data_max) / 10 + 1; // 10%的边距，最少1
            data_min = data_max - margin;
            data_max = data_max + margin;
        }
    }

    // 添加20%的边距，让波形不贴边显示
    int32_t range = data_max - data_min;
    int32_t margin = range / 5; // 20%边距
    if (margin < 1) margin = 1; // 最小边距为1

    int32_t display_min = data_min - margin;
    int32_t display_max = data_max + margin;

    // 设置图表的Y轴范围
    lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y, display_min, display_max);

    // 重置缩放比例到合适的值
    lv_chart_set_zoom_x(ui->Wave_Page_chart_1, 256); // 256 = 无缩放
    lv_chart_set_zoom_y(ui->Wave_Page_chart_1, 256); // 256 = 无缩放

    // 重置滚动位置到中心
    lv_obj_scroll_to(ui->Wave_Page_chart_1, 0, 0, LV_ANIM_ON);

    // 使用安全的刷新机制
    lv_chart_refresh(ui->Wave_Page_chart_1);
    lv_obj_invalidate(ui->Wave_Page_chart_1);

    // ESP_LOGI(TAG, "Auto scale completed: range [%ld, %ld] -> display [%ld, %ld]",
    //          data_min, data_max, display_min, display_max);
}

/**
 * @brief 波形数据更新处理
 */
void ui_scr_wave_data_update(ui_notif_msg_t *msg)
{


    if (!msg || !msg->user_data) {
        return;
    }

    wave_data_t *wave_data = (wave_data_t *)msg->user_data;
    
    // 添加数据到缓存
    wave_add_data_to_buffer(wave_data->type, wave_data->value);

    // 如果是当前显示的数据类型，更新图表
    if (wave_data->type == current_wave_type) {
        lv_ui *ui = &guider_ui;
        if (ui->Wave_Page_chart_1 && ui->Wave_Page_chart_1_series) {
            // last_update_time = current_time;
            // 应用缩放因子
            int32_t scaled_value = (int32_t)(wave_data->value * wave_configs[wave_data->type].scale_factor);
            // 快速更新数据，减少在锁内的时间 - 修复：使用正确的数据系列指针
            lv_chart_set_next_value(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series, scaled_value);

            // 如果启用了自动缩放，定期执行自动缩放
            if (auto_scale_enabled) {
                static uint32_t auto_scale_counter = 0;
                auto_scale_counter++;
                // 每20个数据点执行一次自动缩放，避免过于频繁
                if (auto_scale_counter >= 20) {
                    auto_scale_counter = 0;
                    wave_auto_scale();
                }
            }

            lv_obj_invalidate(ui->Wave_Page_chart_1);

        } else {
            ESP_LOGW(TAG, "Chart or series not available for data update");
        }
    }
}

void ui_notif_scr_wave_task(ui_notif_msg_t *msg)
{
    switch ((int)msg->type)
    {
    case UI_NOTIF_WAVE_DATA_UPDATE:
        ui_scr_wave_data_update(msg);
        break;

    default:
        break;
    }
}
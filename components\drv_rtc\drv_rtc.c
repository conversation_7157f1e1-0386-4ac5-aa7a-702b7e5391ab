#include <stdio.h>
#include <string.h>
#include "time.h"
#include "sys/time.h"
#include "drv_rtc.h"

#include "rtc_pcf8563.h"

#define TAG "rtc"

static bool run_flag;
static rtc_dev_t rtc_dev;

rtc_time_t rtc_time;

rtc_time_t def_rtc_time = {
    .tm_year = 2025,
    .tm_mon = 2,
    .tm_mday = 2,
    .tm_hour = 17,
    .tm_min = 9,
    .tm_sec = 0,
};

static rtc_dev_t dev_list[] = {
    {RTC_DEV_PCF8563, 0xA2, "PCF8563"},
    {RTC_DEV_DS3231, 0xD0, "DS3231"},
};

const char *rtc_week_zh[] = {
#if CALENDAR_WEEK_STARTS_MONDAY
    "一", "二", "三", "四", "五", "六", "日"
#else
    "日", "一", "二", "三", "四", "五", "六"
#endif
};

static void _rtc_read()
{
    switch (rtc_dev.type)
    {
    case RTC_DEV_PCF8563:
        rtc_pcf8563_read(i2c_handler, &rtc_time);
        break;
    case RTC_DEV_DS3231:
        break;
    default:
        break;
    }
}

static void _set_localtime()
{
    struct tm timeinfo = {
        .tm_year = rtc_time.tm_year - 1900, // 2022-1900
        .tm_mon = rtc_time.tm_mon - 1,      // 1月 (0-11)
        .tm_mday = rtc_time.tm_mday,
        .tm_hour = rtc_time.tm_hour, // 北京时间8:00
        .tm_min = rtc_time.tm_min,
        .tm_sec = rtc_time.tm_sec,
    };

    time_t t = mktime(&timeinfo); // 转换为 time_t
    if (t == (time_t)-1)
    {
        ESP_LOGI(TAG, "failed to set time\n");
        return;
    }

    struct timeval tv = {
        .tv_sec = t,
        .tv_usec = 0};

    // 使用 settimeofday 替代方法（ESP-IDF 可能不支持）
    // 替代方案：直接修改系统时间（如果可用）
    if (settimeofday(&tv, NULL) != 0)
    {
        ESP_LOGI(TAG, "settimeofday() not available, using alternative\n");
        time(&t); // 如果无法设置，至少更新当前时间（可能无效）
    }
}

esp_err_t drv_rtc_init()
{
    esp_err_t err = ESP_FAIL;
    run_flag = false;

    if (i2c_handler == NULL)
        return err;

    for (size_t i = 0; i < sizeof(dev_list) / sizeof(dev_list[0]); i++)
    {
        err = i2c_bus_probe_addr(i2c_handler, dev_list[i].addr);
        if (err == ESP_OK)
        {
            ESP_LOGI(TAG, "detected rtc device at 0x%02X, name is: %s",
                     dev_list[i].addr, dev_list[i].name);
            memcpy(&rtc_dev, &dev_list[i], sizeof(dev_list[0]));
            break;
        }
    }

    if (err == ESP_OK)
    {
        if (rtc_dev.type == RTC_DEV_PCF8563)
        {
            rtc_pcf8563_init(i2c_handler);
        }
        else if (rtc_dev.type == RTC_DEV_PCF8563)
        {
        }
    }

    // drv_rtc_set(&def_rtc_time);

    // 初始化读取一次时间
    _rtc_read();

    // 设置本地时间
    _set_localtime();

    return err;
}

void drv_rtc_deinit()
{
    run_flag = false;
    memset(&rtc_dev, 0x0, sizeof(rtc_dev_t));
}

void drv_rtc_set(rtc_time_t *time)
{
    ESP_LOGI(TAG, "rtc time update");

    switch (rtc_dev.type)
    {
    case RTC_DEV_PCF8563:
        rtc_pcf8563_write(i2c_handler, time);
        break;
    case RTC_DEV_DS3231:
        break;
    default:
        break;
    }
}

void drv_rtc_task(void *pvParameters)
{
    periph_service_handle_t serv_handle = (periph_service_handle_t)pvParameters;
    int delay = periph_service_get_data(serv_handle);

    if (i2c_handler == NULL)
        return;

    run_flag = true;
    while (run_flag)
    {
        if (!run_flag)
            break;

        _rtc_read();

        // ESP_LOGI(TAG, "%04d/%02d/%02d %02d:%02d:%02d", rtc_time.tm_year, rtc_time.tm_mon, rtc_time.tm_mday, rtc_time.tm_hour, rtc_time.tm_min, rtc_time.tm_sec);

        vTaskDelay(pdMS_TO_TICKS(delay));
    }

    ESP_LOGI(TAG, "rtc service destroyed");
    vTaskDelete(NULL);
}

const char *drv_rtc_get_weekstr(rtc_time_t *time)
{
    uint8_t week;
    get_day_of_week(time->tm_year, time->tm_mon, time->tm_mday, &week);
    return rtc_week_zh[week];
}

const char *drv_rtc_get_weekstr_byidx(int index)
{
    return rtc_week_zh[index];
}

uint8_t drv_rtc_get_dayofweek(rtc_time_t *time)
{
    uint8_t week;
    get_day_of_week(time->tm_year, time->tm_mon, time->tm_mday, &week);
    return week;
}

bool drv_rtc_detect(void)
{
    esp_err_t err;

    err = i2c_bus_probe_addr(i2c_handler, rtc_dev.addr);

    if (err == ESP_OK)
        return true;

    return false;
}

uint8_t drv_rtc_get_addr(void)
{
    return (rtc_dev.addr >> 1);
}

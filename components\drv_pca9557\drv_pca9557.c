#include <stdio.h>
#include <stdint.h>
#include "drv_pca9557.h"
#include "hal_i2c.h"
#include "esp_log.h"

#define TAG "PCA9557"

#define PCA9557_REG_CONFIG 0x03
#define PCA9557_REG_INPUT 0x00
#define PCA9557_REG_OUTPUT 0x01

static uint8_t i2c_addr = 0x18;
static uint8_t pinmode_p0 = 0xff; // all as input (1= input)
static uint8_t pindata_p0 = 0x00; // all as 0 default

static int modifyBit(int currentByte, int position, int bit) // bit field, change position, change value
{
    int mask = 1 << position;
    return (currentByte & ~mask) | ((bit << position) & mask);
}

void drv_pca9557_init(int ad0, int ad1, int ad2)
{
    uint8_t reg = PCA9557_REG_CONFIG;
    i2c_addr |= (ad0 << 0);
    i2c_addr |= (ad1 << 1);
    i2c_addr |= (ad2 << 2);

    ESP_LOGI(TAG, "pca9557 address 0x%X", i2c_addr);

    i2c_addr <<= 1;
    i2c_bus_write_bytes(i2c_handler, i2c_addr, &reg, 1, &pinmode_p0, 1);
}

void drv_pca9557_pinmode(uint8_t pin, uint8_t mode)
{
    uint8_t reg = PCA9557_REG_CONFIG;
    pinmode_p0 = modifyBit(pinmode_p0, pin, !mode); // change relavent bit in pin mode (!mode use for match with arduino mode)

    i2c_bus_write_bytes(i2c_handler, i2c_addr, &reg, 1, &pinmode_p0, 1);
}

void drv_pca9557_write(uint8_t pin, uint8_t mode)
{
    uint8_t reg = PCA9557_REG_OUTPUT;
    pindata_p0 = modifyBit(pindata_p0, pin, mode); // change relavent bit in pin data

    i2c_bus_write_bytes(i2c_handler, i2c_addr, &reg, 1, &pindata_p0, 1);
}

uint8_t drv_pca9557_read(uint8_t pin)
{
    uint8_t reg = PCA9557_REG_INPUT;
    uint8_t data;

    i2c_bus_read_bytes(i2c_handler, i2c_addr, &reg, 1, &data, 1);

    ESP_LOGD(TAG, "extern IO data: 0x%x", data);

    return (data >> pin) & 1;
}

uint8_t drv_pca9557_read_output(uint8_t pin)
{
    return (pindata_p0 >> pin) & 1;
}

bool drv_pca9557_detect(void)
{
    esp_err_t err;

    err = i2c_bus_probe_addr(i2c_handler, i2c_addr);

    if (err == ESP_OK)
        return true;

    return false;
}

uint8_t drv_pca9557_get_addr()
{
    return (i2c_addr >> 1);
}

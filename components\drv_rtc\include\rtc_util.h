#ifndef _RTC_UTIL_H
#define _RTC_UTIL_H

#include <stdint.h>

#define CALENDAR_WEEK_STARTS_MONDAY 0  //宏定义开启，表示返回0对应星期一，不开启表示返回0对应星期日

/**
 * Get the day of the week
 * @param year a year
 * @param month a  month [1..12]
 * @param day a day [1..32]
 * @return [0..6] which means [Sun..Sat] or [Mon..Sun] depending on LV_CALENDAR_WEEK_STARTS_MONDAY
 */
static inline void get_day_of_week(uint32_t year, uint32_t month, uint32_t day, uint8_t *week)
{
    uint32_t a = month < 3 ? 1 : 0;
    uint32_t b = year - a;

#if CALENDAR_WEEK_STARTS_MONDAY
    uint32_t day_of_week = (day + (31 * (month - 2 + 12 * a) / 12) + b + (b / 4) - (b / 100) + (b / 400) - 1) % 7;
#else
    uint32_t day_of_week = (day + (31 * (month - 2 + 12 * a) / 12) + b + (b / 4) - (b / 100) + (b / 400)) % 7;
#endif

    *week = day_of_week;
}


#endif



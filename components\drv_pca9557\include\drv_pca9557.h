#ifndef _DRV_PCA9557_H
#define _DRV_PCA9557_H

#include <stdbool.h>

// pin ports
/**
 * po_0 = 0
   po_1   1
   po_2   2
   po_3   3
   po_4   4
   po_5   5
   po_6   6
   po_7   7
 */

enum{
    EXTEND_LOW,
    EXTEND_HIGH,
};

enum{
  EXTEND_INPUT,
  EXTEND_OUTPUT,
};

enum
{
    EXTEND_PIN_0_XX,
    EXTEND_PIN_1_PWR_ON,
    EXTEND_PIN_2_CHAT_LISTEN,
    EXTEND_PIN_3_BAT_CTRL,
    EXTEND_PIN_4_BAT_CHRG,
    EXTEND_PIN_5_BEEP_CTRL,
    EXTEND_PIN_6_BLE_CTRL,
    EXTEND_PIN_7_XX,
};

void drv_pca9557_init(int ad0, int ad1, int ad2);
void drv_pca9557_pinmode(uint8_t pin, uint8_t mode);
void drv_pca9557_write(uint8_t pin, uint8_t mode);
uint8_t drv_pca9557_read(uint8_t pin);
uint8_t drv_pca9557_read_output(uint8_t pin);
bool drv_pca9557_detect(void);
uint8_t drv_pca9557_get_addr(void);

#endif

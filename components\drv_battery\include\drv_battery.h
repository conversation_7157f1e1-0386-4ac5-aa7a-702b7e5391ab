#ifndef _DRV_BATTERY_H
#define _DRV_BATTERY_H

#include <stdbool.h>
#include <stdint.h>

typedef enum
{
    BATTERY_STATE_CHRG = 0x01,
    BATTERY_STATE_NOCHRG,
} battery_state_chrg_t;

typedef enum
{
    BATTERY_STATE_0,
    BATTERY_STATE_25,
    BATTERY_STATE_50,
    BATTERY_STATE_75,
    BATTERY_STATE_100,
} battery_state_t;

typedef struct
{
    float value;                     // 电池电压
    int pct;                         // 百分比
    battery_state_t state;           // 电量状态
    battery_state_chrg_t state_chrg; // 充电状态
} battery_info_t;

void drv_battery_init();
void drv_battery_deinit(void);
void drv_battery_send_data(uint16_t *data);
void drv_battery_report_sw(bool state);
void drv_battery_report_freq(int freq);

extern battery_info_t battery_info;

#endif

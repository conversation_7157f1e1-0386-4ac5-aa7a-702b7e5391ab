/* * Data converted from C:/Users/<USER>/Desktop/DD_Clock-develop/DDClock/main/www/index_ota_update.html.gz
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global index_ota_update_html_gz
index_ota_update_html_gz:

.global _binary_index_ota_update_html_gz_start
_binary_index_ota_update_html_gz_start: /* for objcopy compatibility */
.byte 0x1f, 0x8b, 0x08, 0x08, 0x90, 0x6e, 0x14, 0x68, 0x04, 0x00, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f
.byte 0x6f, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x68, 0x74, 0x6d, 0x6c, 0x00
.byte 0xbd, 0x58, 0xc9, 0xce, 0xd3, 0x30, 0x10, 0x3e, 0xf3, 0x3f, 0x85, 0x09, 0x42, 0x69, 0x81, 0xa4
.byte 0x2d, 0x8b, 0x58, 0xda, 0x20, 0xb1, 0x0a, 0x24, 0x36, 0xb1, 0x48, 0x48, 0x80, 0x90, 0x13, 0x3b
.byte 0xad, 0xc1, 0xb5, 0x83, 0xed, 0x94, 0x16, 0x84, 0xc4, 0x0d, 0x21, 0xc4, 0x95, 0x0b, 0x0f, 0x80
.byte 0x78, 0x01, 0x0e, 0xf0, 0x3a, 0x2c, 0xe2, 0x2d, 0x18, 0x3b, 0x4d, 0xd3, 0x36, 0x6d, 0x58, 0x04
.byte 0xe4, 0x3f, 0x34, 0xb6, 0x67, 0xbe, 0x99, 0xf1, 0x7c, 0x33, 0x76, 0xfe, 0xc1, 0x6e, 0x22, 0x13
.byte 0x33, 0xcb, 0x28, 0x1a, 0x99, 0x31, 0x3f, 0xb9, 0x33, 0x28, 0x7e, 0xe0, 0x97, 0x62, 0x72, 0x72
.byte 0x07, 0xc1, 0x33, 0x18, 0x53, 0x83, 0x51, 0x32, 0xc2, 0x4a, 0x53, 0x13, 0x79, 0xb9, 0x49, 0x83
.byte 0x63, 0xde, 0xf2, 0x92, 0xc0, 0x63, 0x1a, 0x79, 0x13, 0x46, 0x1f, 0x67, 0x52, 0x19, 0x0f, 0x25
.byte 0x52, 0x18, 0x2a, 0x40, 0xf4, 0x31, 0x23, 0x66, 0x14, 0x11, 0x3a, 0x61, 0x09, 0x0d, 0xdc, 0xe0
.byte 0x00, 0x13, 0xcc, 0x30, 0xcc, 0x03, 0x9d, 0x60, 0x4e, 0xa3, 0x5e, 0x89, 0x63, 0x98, 0xe1, 0xf4
.byte 0xe4, 0xd9, 0xb3, 0x67, 0xb8, 0x4c, 0x1e, 0xa2, 0xab, 0x37, 0x4f, 0xa1, 0x5b, 0x19, 0xc1, 0x86
.byte 0x0e, 0x3a, 0x6e, 0x05, 0xfc, 0xea, 0x58, 0x87, 0xac, 0x63, 0xda, 0xcc, 0x60, 0xc2, 0xa9, 0xed
.byte 0x49, 0x19, 0xa7, 0x01, 0x13, 0x59, 0x6e, 0x0e, 0xb8, 0x09, 0xf7, 0x8a, 0x9e, 0xc2, 0x7b, 0xf1
.byte 0x38, 0x9b, 0x27, 0x50, 0xaf, 0xdb, 0xdd, 0xdb, 0x5f, 0x4c, 0x8e, 0x28, 0x1b, 0x8e, 0xcc, 0x09
.byte 0x74, 0xf8, 0x70, 0x36, 0xad, 0x66, 0x63, 0xa9, 0x08, 0x55, 0x81, 0xc2, 0x84, 0xe5, 0x1a, 0x16
.byte 0x97, 0xd7, 0xc6, 0x58, 0x0d, 0x99, 0xb0, 0x38, 0xd9, 0x14, 0xe1, 0xdc, 0xc8, 0x6a, 0x29, 0x85
.byte 0x58, 0x03, 0xcd, 0x9e, 0x50, 0x58, 0x3d, 0x92, 0x4d, 0xdd, 0xfc, 0xb3, 0x9d, 0x8d, 0xce, 0xc4
.byte 0x38, 0x79, 0x38, 0x54, 0x32, 0x17, 0xe4, 0x04, 0x78, 0xde, 0xb3, 0x7f, 0xeb, 0xe6, 0x4f, 0xa0
.byte 0x6e, 0x35, 0x95, 0x61, 0x42, 0x98, 0x18, 0xc2, 0xdc, 0x0a, 0x74, 0x21, 0x4d, 0x66, 0xdb, 0x90
.byte 0x0f, 0x1d, 0x3e, 0x7e, 0x8c, 0xc4, 0x6b, 0x1e, 0xa6, 0x78, 0xcc, 0xf8, 0xec, 0x04, 0xd2, 0x58
.byte 0xe8, 0x40, 0x53, 0xc5, 0xd2, 0xcd, 0x21, 0xac, 0x84, 0x9d, 0x48, 0x2e, 0x15, 0x20, 0x1e, 0x3d
.byte 0x7a, 0xb4, 0x34, 0x5e, 0xed, 0x7a, 0x19, 0x5d, 0xdd, 0xdb, 0x7a, 0x50, 0x3d, 0xd8, 0x36, 0x2d
.byte 0x39, 0x23, 0x68, 0x0f, 0x21, 0xa4, 0x5a, 0xe7, 0x4c, 0xd0, 0x60, 0x73, 0x3a, 0x0c, 0x9d, 0x9a
.byte 0x00, 0x73, 0x36, 0x84, 0x6d, 0x4f, 0x28, 0xf0, 0x49, 0x55, 0x6b, 0x84, 0xe9, 0x8c, 0x63, 0x88
.byte 0x26, 0xb6, 0x6c, 0x59, 0xf2, 0x37, 0x57, 0xda, 0x3a, 0x9c, 0x49, 0x66, 0x15, 0x56, 0x7c, 0x8e
.byte 0xb1, 0x3a, 0x50, 0xbc, 0x65, 0x6a, 0x08, 0x83, 0x8d, 0xbb, 0x17, 0xb8, 0x88, 0x17, 0xd9, 0xd9
.byte 0x42, 0x8e, 0x82, 0x07, 0x2b, 0xe8, 0x3f, 0x05, 0x9c, 0x27, 0x65, 0x9d, 0x98, 0x75, 0x5a, 0xd6
.byte 0xc1, 0x53, 0xa9, 0xc6, 0x5b, 0x59, 0x94, 0xa6, 0xcb, 0x2c, 0x9d, 0x06, 0x73, 0xdc, 0x83, 0x47
.byte 0x8e, 0x6d, 0xe2, 0xef, 0xd1, 0x23, 0x25, 0x7f, 0xeb, 0x69, 0x3b, 0xd4, 0x6d, 0xa8, 0x86, 0x23
.byte 0xcd, 0xa9, 0x59, 0x76, 0x37, 0x8c, 0x8d, 0xf8, 0x55, 0x6a, 0xba, 0xdd, 0x29, 0xc2, 0xf8, 0x59
.byte 0x16, 0x07, 0x9d, 0x79, 0xdd, 0xef, 0x0c, 0x2c, 0xf9, 0xe7, 0x6d, 0x83, 0xb0, 0x49, 0xf1, 0xe6
.byte 0x46, 0x6e, 0xa7, 0xa0, 0x23, 0x8d, 0x24, 0x89, 0xfc, 0x6b, 0x57, 0x6f, 0xdc, 0xf4, 0x11, 0x4e
.byte 0x0c, 0x93, 0x22, 0xf2, 0xf7, 0xf8, 0x88, 0x0a, 0xd7, 0xe7, 0x22, 0x7f, 0x9c, 0x73, 0xc3, 0x32
.byte 0xac, 0x4c, 0xc7, 0x2a, 0x04, 0xd0, 0x66, 0xb0, 0x8f, 0x18, 0xa8, 0xe4, 0x19, 0x97, 0x98, 0xdc
.byte 0xb7, 0xb3, 0x7e, 0x01, 0xbb, 0x80, 0x1e, 0xf5, 0xca, 0xe6, 0xf4, 0xe5, 0xed, 0xc7, 0xcf, 0x9f
.byte 0x3e, 0x7c, 0x79, 0xfd, 0xe2, 0xdb, 0xc7, 0x77, 0xd0, 0x94, 0x7a, 0x6b, 0x82, 0xb1, 0x5a, 0x9b
.byte 0x28, 0xea, 0xa4, 0xb0, 0x6c, 0x0b, 0xc7, 0x77, 0xed, 0x12, 0x8c, 0xb9, 0xfe, 0x66, 0x0d, 0x97
.byte 0xf3, 0x52, 0x40, 0x8f, 0x15, 0x43, 0x58, 0xd3, 0x79, 0xdc, 0x32, 0x23, 0xa6, 0xdb, 0x3e, 0x72
.byte 0x51, 0x47, 0x25, 0xe3, 0x85, 0x14, 0x74, 0x0d, 0x9f, 0xe3, 0x98, 0x72, 0x87, 0x52, 0x95, 0xa5
.byte 0x6f, 0x49, 0x33, 0x87, 0x3d, 0xf9, 0xfd, 0xf9, 0xcb, 0xaf, 0xaf, 0xde, 0x7f, 0x7d, 0xf3, 0x02
.byte 0xdc, 0x0e, 0xc3, 0x70, 0xd0, 0x71, 0x1a, 0x0d, 0x5e, 0xc6, 0xb9, 0x31, 0x52, 0xac, 0x6c, 0x49
.byte 0x6c, 0x60, 0x9c, 0x70, 0xac, 0x75, 0x64, 0xd3, 0x3b, 0xc1, 0x3c, 0x07, 0x41, 0xd7, 0xa2, 0x0b
.byte 0xc7, 0x39, 0x4b, 0x1e, 0x46, 0x5e, 0x21, 0xdd, 0x6a, 0x7b, 0xb5, 0x4d, 0x69, 0x98, 0x28, 0x13
.byte 0xe9, 0xec, 0x41, 0x71, 0xfa, 0x27, 0x07, 0x1d, 0x18, 0xfe, 0x86, 0x06, 0x54, 0x5f, 0x95, 0xaf
.byte 0xba, 0x04, 0x2c, 0x6f, 0xc4, 0xfc, 0x99, 0x99, 0x81, 0xe3, 0x07, 0x8c, 0x2b, 0xd9, 0xe2, 0xbd
.byte 0x22, 0xa0, 0x1b, 0xea, 0x44, 0xb1, 0xcc, 0xac, 0x02, 0xa5, 0xb9, 0x70, 0xcc, 0x43, 0x36, 0x95
.byte 0x32, 0x7e, 0xd0, 0x5e, 0x94, 0x44, 0xf5, 0x4c, 0xa0, 0x69, 0xd8, 0x14, 0x5d, 0x01, 0x3e, 0xa0
.byte 0x08, 0x81, 0x54, 0xe8, 0x36, 0x36, 0x84, 0x5c, 0x33, 0xd3, 0xf2, 0xef, 0xde, 0xf5, 0xdb, 0xfd
.byte 0x9a, 0x16, 0x1c, 0xd7, 0xf9, 0x18, 0xaa, 0x2e, 0x1c, 0x52, 0x73, 0x8e, 0x53, 0xfb, 0x7a, 0x7a
.byte 0x76, 0x91, 0xb4, 0x96, 0xd3, 0xdf, 0x0e, 0x99, 0x10, 0x54, 0x5d, 0xb8, 0x79, 0xf9, 0x12, 0x00
.byte 0x97, 0x36, 0xee, 0x94, 0x2f, 0x21, 0xa7, 0x62, 0x68, 0x46, 0x28, 0x40, 0xbd, 0x7b, 0xab, 0x06
.byte 0x9e, 0x6d, 0x0e, 0x62, 0x9e, 0xd7, 0xa6, 0x20, 0xae, 0x61, 0x00, 0x8c, 0x7e, 0xd7, 0xbb, 0xfe
.byte 0x56, 0xbc, 0x8b, 0x56, 0xb4, 0x01, 0xd0, 0xb3, 0x42, 0x5e, 0x3b, 0xb4, 0x3f, 0xba, 0xbf, 0xb3
.byte 0x0b, 0x1e, 0xab, 0xea, 0xb8, 0x7d, 0x4d, 0xc9, 0x61, 0x93, 0x2a, 0x50, 0xc6, 0x6b, 0x57, 0x3a
.byte 0xc0, 0x8f, 0x26, 0x69, 0x58, 0x2e, 0xa5, 0x17, 0x2a, 0xb9, 0xe2, 0xa0, 0xe2, 0x75, 0x8a, 0x22
.byte 0xf6, 0xfa, 0x3b, 0xb5, 0x38, 0x58, 0x8a, 0x5a, 0x8b, 0x38, 0xca, 0x0d, 0x8f, 0x22, 0xd4, 0x2d
.byte 0x36, 0xb1, 0xfe, 0xc0, 0x75, 0x48, 0x99, 0x96, 0x77, 0x45, 0xba, 0xf0, 0x91, 0xa6, 0x9c, 0x26
.byte 0x86, 0x92, 0xdd, 0xde, 0x06, 0x0e, 0x3c, 0x43, 0x94, 0x6b, 0xea, 0x6c, 0x94, 0x7b, 0xff, 0x1b
.byte 0x26, 0xce, 0x5b, 0xfc, 0xcc, 0xe6, 0xcb, 0xf2, 0x93, 0xaa, 0x09, 0x55, 0x88, 0x69, 0x24, 0xa4
.byte 0x81, 0x91, 0xf9, 0x65, 0x83, 0x4c, 0x10, 0x3a, 0xbd, 0x9a, 0xb6, 0x7c, 0xe4, 0xb7, 0xd1, 0xc9
.byte 0x3f, 0x33, 0x9b, 0x60, 0x61, 0xcd, 0x8e, 0xf0, 0x04, 0x22, 0xce, 0x70, 0x42, 0xf5, 0xaf, 0x9a
.byte 0xbf, 0x53, 0xbe, 0x2c, 0x93, 0xd9, 0x46, 0xef, 0x77, 0xfc, 0x5f, 0x71, 0xc4, 0x36, 0xe1, 0x22
.byte 0xe4, 0x8c, 0x26, 0x2c, 0x65, 0x94, 0x20, 0x9c, 0xc2, 0x71, 0xe3, 0x3c, 0x6c, 0x70, 0x62, 0x0e
.byte 0xfd, 0xab, 0x55, 0x59, 0xd2, 0x14, 0x1a, 0x38, 0x8e, 0x39, 0x18, 0x89, 0x90, 0x51, 0x39, 0xed
.byte 0xff, 0x1e, 0x48, 0xd5, 0x85, 0x37, 0x41, 0x39, 0xac, 0x6d, 0x75, 0x04, 0x52, 0x0b, 0x1a, 0xde
.byte 0xe9, 0xde, 0xeb, 0x6f, 0x15, 0x9e, 0x8e, 0x8c, 0xc9, 0x40, 0x5a, 0xd0, 0xc7, 0xe8, 0xf6, 0xe5
.byte 0x4b, 0x17, 0x60, 0x74, 0x9d, 0x3e, 0xca, 0xa9, 0x36, 0xad, 0xf6, 0x46, 0xad, 0x42, 0x23, 0x94
.byte 0x42, 0xc1, 0xc5, 0x7c, 0xa6, 0x0d, 0x14, 0x82, 0x3b, 0xbf, 0xc0, 0x64, 0xd5, 0x39, 0xd6, 0x7a
.byte 0x46, 0xbd, 0x46, 0x0a, 0x10, 0x07, 0x71, 0xc3, 0x42, 0xd8, 0x14, 0x1e, 0x2e, 0x95, 0x7e, 0xae
.byte 0x68, 0xcd, 0xe6, 0xda, 0x2a, 0x1d, 0xec, 0xd6, 0x08, 0xb8, 0x95, 0x03, 0x0b, 0xab, 0x3a, 0x93
.byte 0x42, 0xd3, 0x9b, 0x70, 0xa1, 0x69, 0xf7, 0x7f, 0xaa, 0x0a, 0xa7, 0x3f, 0xb6, 0x41, 0x81, 0x5e
.byte 0xd1, 0x0e, 0x9b, 0x34, 0x2a, 0xce, 0xd6, 0x5d, 0xfd, 0x0d, 0x47, 0xbd, 0x1b, 0xf3, 0x42, 0xe1
.byte 0x52, 0x43, 0xca, 0xcd, 0x88, 0xda, 0xaf, 0x2b, 0x41, 0x8b, 0xdd, 0xc5, 0xb1, 0xca, 0x33, 0xc3
.byte 0x67, 0xbb, 0xbd, 0x7f, 0xe5, 0xfe, 0xaf, 0xfa, 0xb9, 0x12, 0xe2, 0x7e, 0xe4, 0xa1, 0x73, 0x4a
.byte 0x49, 0xb5, 0xfb, 0xae, 0xf0, 0x60, 0xf4, 0x3f, 0x76, 0x7b, 0xe7, 0xf7, 0x56, 0x9e, 0x35, 0x51
.byte 0xba, 0x28, 0x37, 0x60, 0x76, 0x06, 0x07, 0x09, 0xb8, 0xad, 0x57, 0x18, 0x4d, 0x27, 0x50, 0x97
.byte 0xcd, 0xb4, 0x2e, 0x84, 0xca, 0xb6, 0x7f, 0x46, 0x8e, 0xa1, 0xf8, 0x6c, 0xcd, 0xd6, 0xb5, 0xea
.byte 0x75, 0x98, 0x41, 0xb6, 0xa3, 0x12, 0xc0, 0xfa, 0x01, 0x69, 0xef, 0xa0, 0x62, 0x68, 0xa4, 0xc1
.byte 0xfc, 0x27, 0x7b, 0xd7, 0xe9, 0x00, 0x41, 0xe0, 0x63, 0x8b, 0x82, 0xf2, 0xb0, 0xe5, 0xdf, 0x72
.byte 0xb1, 0xa0, 0x6b, 0xf3, 0x48, 0x4e, 0x20, 0x1f, 0xed, 0xb7, 0x36, 0xec, 0xc5, 0xdd, 0x3a, 0xc6
.byte 0xa9, 0xa1, 0x80, 0x7b, 0x9e, 0x4d, 0x29, 0x69, 0x1d, 0x6c, 0xc3, 0xa2, 0xbf, 0xd7, 0x6f, 0x36
.byte 0x51, 0x1d, 0xb2, 0x2b, 0xd7, 0x0c, 0x3f, 0x5b, 0xb1, 0x71, 0xd9, 0xf6, 0x66, 0x77, 0xf1, 0x6f
.byte 0xd9, 0x90, 0xf6, 0xd9, 0x8f, 0xf0, 0x39, 0x3c, 0x1c, 0xa6, 0xc5, 0x03, 0x47, 0x6b, 0xe8, 0x2e
.byte 0xb8, 0xa1, 0xfb, 0x6e, 0x01, 0x90, 0x06, 0xad, 0xbf, 0x99, 0x61, 0x99, 0x51, 0xd1, 0xf2, 0xec
.byte 0x27, 0x82, 0x77, 0xc0, 0x1e, 0xe6, 0x07, 0x5c, 0x2b, 0x6d, 0xec, 0x73, 0x9a, 0x0a, 0xe2, 0x4e
.byte 0x9f, 0x4d, 0x47, 0xc3, 0xda, 0x25, 0x6a, 0xe9, 0x0a, 0x59, 0x5e, 0x0e, 0xe1, 0x03, 0xc6, 0xfd
.byte 0x67, 0xe5, 0x07, 0xc0, 0xd6, 0xbe, 0xbc, 0x70, 0x11, 0x00, 0x00

.global _binary_index_ota_update_html_gz_end
_binary_index_ota_update_html_gz_end: /* for objcopy compatibility */


.global index_ota_update_html_gz_length
index_ota_update_html_gz_length:
.long 1419
